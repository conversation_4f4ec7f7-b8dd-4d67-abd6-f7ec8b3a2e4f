{"ast": null, "code": "import _ from 'lodash';\nexport const DraftManager = {\n  updateDraft: function (draftParams, files, section) {\n    let {\n      fieldValues: data,\n      update_type_id,\n      isDynamicForm,\n      hasFeatureAccess\n    } = draftParams;\n    if (update_type_id == 'sbtsk_can_postpone' || update_type_id == 'sbtsk_can_reject' || isDynamicForm == true && !hasFeatureAccess) {\n      return;\n    }\n    let localStorageKey = this.getDraftKey(draftParams);\n    // let data = this.formRef?.current.getFieldsValue();\n    let existingDraft = localStorage.getItem(localStorageKey);\n    existingDraft = JSON.parse(existingDraft);\n    function createDraftFromUpdates(currentData, updates) {\n      //section is attachment based fields\n      if (section) {\n        // Iterate over each category in updates.section\n        for (const category in updates[section]) {\n          // Check if the category exists in updates\n          if (updates[section].hasOwnProperty(category)) {\n            var _currentData, _currentData2, _currentData2$section, _currentData4, _currentData4$section, _updates$section;\n            if (!((_currentData = currentData) === null || _currentData === void 0 ? void 0 : _currentData[section])) {\n              currentData = {\n                ...currentData,\n                [section]: {}\n              };\n            }\n            if (!((_currentData2 = currentData) === null || _currentData2 === void 0 ? void 0 : (_currentData2$section = _currentData2[section]) === null || _currentData2$section === void 0 ? void 0 : _currentData2$section.category)) {\n              var _currentData3;\n              currentData = {\n                ...currentData,\n                [section]: {\n                  ...((_currentData3 = currentData) === null || _currentData3 === void 0 ? void 0 : _currentData3[section]),\n                  [category]: []\n                }\n              };\n            }\n            const currentItems = (_currentData4 = currentData) === null || _currentData4 === void 0 ? void 0 : (_currentData4$section = _currentData4[section]) === null || _currentData4$section === void 0 ? void 0 : _currentData4$section[category];\n            const updatedItems = updates === null || updates === void 0 ? void 0 : (_updates$section = updates[section]) === null || _updates$section === void 0 ? void 0 : _updates$section[category];\n            // More items in updates means we replace; fewer means we filter\n            if (((updatedItems === null || updatedItems === void 0 ? void 0 : updatedItems.length) || 0) > ((currentItems === null || currentItems === void 0 ? void 0 : currentItems.length) || 0)) {\n              currentData[section][category] = updatedItems;\n              // break;\n            } else if (((updatedItems === null || updatedItems === void 0 ? void 0 : updatedItems.length) || 0) < ((currentItems === null || currentItems === void 0 ? void 0 : currentItems.length) || 0)) {\n              currentData[section][category] = currentItems.filter(item => updatedItems.includes(item));\n              // break;\n            }\n          }\n        }\n      } else {\n        for (const category in updates) {\n          if (category != section) {\n            currentData[category] = updates[category];\n          }\n        }\n      }\n      return currentData;\n    }\n    if (!existingDraft) {\n      existingDraft = {};\n    }\n    data = _.merge(data, this.getFilesSectionsFormData2(files, section));\n    const updatedDraft = createDraftFromUpdates(existingDraft, data);\n    localStorage.setItem(localStorageKey, JSON.stringify(updatedDraft));\n  },\n  applyDraft: function (draftParams, forceApplyForDynamicForm = false) {\n    const {\n      isDynamicForm\n    } = draftParams;\n    if (isDynamicForm == true && !forceApplyForDynamicForm) {\n      return false;\n    }\n    let localStorageKey = this.getDraftKey(draftParams);\n    let draftData = localStorage.getItem(localStorageKey);\n    if (draftData) {\n      draftData = JSON.parse(draftData);\n    }\n    return draftData;\n  },\n  clearDraft: function (draftParams) {\n    localStorage.removeItem(this.getDraftKey(draftParams));\n  },\n  getDraftKey: function (draftParams) {\n    let {\n      sbtsk_id,\n      update_type_id\n    } = draftParams;\n    return `sbtskDraftData_${sbtsk_id}_${update_type_id}`; //make it a method\n  },\n  getFilesSectionsFormData: function (draftParams) {\n    const {\n      filesBySection,\n      micRecordingsBySection,\n      cameraRecordingsBySection\n    } = draftParams.state;\n    // debugger;\n    let data = {};\n\n    // Function to remove duplicates from arrays within an object\n    function removeDuplicatesFromObject(obj) {\n      for (let key in obj) {\n        if (Array.isArray(obj[key])) {\n          obj[key] = [...new Set(obj[key])]; // Remove duplicates\n        }\n      }\n      return obj;\n    }\n\n    // Remove duplicates from each section\n    data['attachments'] = removeDuplicatesFromObject(filesBySection);\n    data['mic_files'] = removeDuplicatesFromObject(micRecordingsBySection);\n    data['camera_files'] = removeDuplicatesFromObject(cameraRecordingsBySection);\n    return data;\n  },\n  getFilesSectionsFormData2: function (files, section) {\n    // debugger;\n    let data = {};\n\n    // Function to remove duplicates from arrays within an object\n    function removeDuplicatesFromObject(obj) {\n      for (let key in obj) {\n        if (Array.isArray(obj[key])) {\n          obj[key] = [...new Set(obj[key])]; // Remove duplicates\n        }\n      }\n      return obj;\n    }\n\n    // Remove duplicates from each section\n    data[section] = removeDuplicatesFromObject(files);\n    return data;\n  }\n};", "map": {"version": 3, "names": ["_", "DraftManager", "updateDraft", "draftParams", "files", "section", "field<PERSON><PERSON><PERSON>", "data", "update_type_id", "isDynamicForm", "hasFeatureAccess", "localStorageKey", "getDraftKey", "existingDraft", "localStorage", "getItem", "JSON", "parse", "createDraftFromUpdates", "currentData", "updates", "category", "hasOwnProperty", "_currentData", "_currentData2", "_currentData2$section", "_currentData4", "_currentData4$section", "_updates$section", "_currentData3", "currentItems", "updatedItems", "length", "filter", "item", "includes", "merge", "getFilesSectionsFormData2", "updatedDraft", "setItem", "stringify", "applyDraft", "forceApplyForDynamicForm", "draftData", "clearDraft", "removeItem", "sbtsk_id", "getFilesSectionsFormData", "filesBySection", "micRecordingsBySection", "cameraRecordingsBySection", "state", "removeDuplicatesFromObject", "obj", "key", "Array", "isArray", "Set"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/routes/my-tasks/DraftManager.js"], "sourcesContent": ["import _ from 'lodash';\r\n\r\nexport const DraftManager = {\r\n    updateDraft: function (draftParams, files, section) {\r\n        let {\r\n            fieldValues: data,\r\n            update_type_id,\r\n            isDynamicForm,\r\n            hasFeatureAccess,\r\n        } = draftParams;\r\n        if (\r\n            update_type_id == 'sbtsk_can_postpone' ||\r\n            update_type_id == 'sbtsk_can_reject' ||\r\n            (isDynamicForm == true && !hasFeatureAccess)\r\n        ) {\r\n            return;\r\n        }\r\n        let localStorageKey = this.getDraftKey(draftParams);\r\n        // let data = this.formRef?.current.getFieldsValue();\r\n        let existingDraft = localStorage.getItem(localStorageKey);\r\n        existingDraft = JSON.parse(existingDraft);\r\n\r\n        function createDraftFromUpdates(currentData, updates) {\r\n            //section is attachment based fields\r\n            if (section) {\r\n                // Iterate over each category in updates.section\r\n                for (const category in updates[section]) {\r\n                    // Check if the category exists in updates\r\n                    if (updates[section].hasOwnProperty(category)) {\r\n                        if (!currentData?.[section]) {\r\n                            currentData = {\r\n                                ...currentData,\r\n                                [section]: {},\r\n                            };\r\n                        }\r\n                        if (!currentData?.[section]?.category) {\r\n                            currentData = {\r\n                                ...currentData,\r\n                                [section]: {\r\n                                    ...currentData?.[section],\r\n                                    [category]: [],\r\n                                },\r\n                            };\r\n                        }\r\n                        const currentItems = currentData?.[section]?.[category];\r\n                        const updatedItems = updates?.[section]?.[category];\r\n                        // More items in updates means we replace; fewer means we filter\r\n                        if (\r\n                            (updatedItems?.length || 0) >\r\n                            (currentItems?.length || 0)\r\n                        ) {\r\n                            currentData[section][category] = updatedItems;\r\n                            // break;\r\n                        } else if (\r\n                            (updatedItems?.length || 0) <\r\n                            (currentItems?.length || 0)\r\n                        ) {\r\n                            currentData[section][category] =\r\n                                currentItems.filter((item) =>\r\n                                    updatedItems.includes(item)\r\n                                );\r\n                            // break;\r\n                        }\r\n                    }\r\n                }\r\n            } else {\r\n                for (const category in updates) {\r\n                    if (category != section) {\r\n                        currentData[category] = updates[category];\r\n                    }\r\n                }\r\n            }\r\n\r\n            return currentData;\r\n        }\r\n\r\n        if (!existingDraft) {\r\n            existingDraft = {};\r\n        }\r\n\r\n        data = _.merge(data, this.getFilesSectionsFormData2(files, section));\r\n        const updatedDraft = createDraftFromUpdates(existingDraft, data);\r\n        localStorage.setItem(localStorageKey, JSON.stringify(updatedDraft));\r\n    },\r\n\r\n    applyDraft: function (draftParams, forceApplyForDynamicForm = false) {\r\n        const { isDynamicForm } = draftParams;\r\n\r\n        if (isDynamicForm == true && !forceApplyForDynamicForm) {\r\n            return false;\r\n        }\r\n\r\n        let localStorageKey = this.getDraftKey(draftParams);\r\n        let draftData = localStorage.getItem(localStorageKey);\r\n\r\n        if (draftData) {\r\n            draftData = JSON.parse(draftData);\r\n        }\r\n\r\n        return draftData;\r\n    },\r\n\r\n    clearDraft: function (draftParams) {\r\n        localStorage.removeItem(this.getDraftKey(draftParams));\r\n    },\r\n\r\n    getDraftKey: function (draftParams) {\r\n        let { sbtsk_id, update_type_id } = draftParams;\r\n        return `sbtskDraftData_${sbtsk_id}_${update_type_id}`; //make it a method\r\n    },\r\n\r\n    getFilesSectionsFormData: function (draftParams) {\r\n        const {\r\n            filesBySection,\r\n            micRecordingsBySection,\r\n            cameraRecordingsBySection,\r\n        } = draftParams.state;\r\n        // debugger;\r\n        let data = {};\r\n\r\n        // Function to remove duplicates from arrays within an object\r\n        function removeDuplicatesFromObject(obj) {\r\n            for (let key in obj) {\r\n                if (Array.isArray(obj[key])) {\r\n                    obj[key] = [...new Set(obj[key])]; // Remove duplicates\r\n                }\r\n            }\r\n            return obj;\r\n        }\r\n\r\n        // Remove duplicates from each section\r\n        data['attachments'] = removeDuplicatesFromObject(filesBySection);\r\n        data['mic_files'] = removeDuplicatesFromObject(micRecordingsBySection);\r\n        data['camera_files'] = removeDuplicatesFromObject(\r\n            cameraRecordingsBySection\r\n        );\r\n\r\n        return data;\r\n    },\r\n\r\n    getFilesSectionsFormData2: function (files, section) {\r\n        // debugger;\r\n        let data = {};\r\n\r\n        // Function to remove duplicates from arrays within an object\r\n        function removeDuplicatesFromObject(obj) {\r\n            for (let key in obj) {\r\n                if (Array.isArray(obj[key])) {\r\n                    obj[key] = [...new Set(obj[key])]; // Remove duplicates\r\n                }\r\n            }\r\n            return obj;\r\n        }\r\n\r\n        // Remove duplicates from each section\r\n        data[section] = removeDuplicatesFromObject(files);\r\n        return data;\r\n    },\r\n};\r\n"], "mappings": "AAAA,OAAOA,CAAC,MAAM,QAAQ;AAEtB,OAAO,MAAMC,YAAY,GAAG;EACxBC,WAAW,EAAE,SAAAA,CAAUC,WAAW,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAChD,IAAI;MACAC,WAAW,EAAEC,IAAI;MACjBC,cAAc;MACdC,aAAa;MACbC;IACJ,CAAC,GAAGP,WAAW;IACf,IACIK,cAAc,IAAI,oBAAoB,IACtCA,cAAc,IAAI,kBAAkB,IACnCC,aAAa,IAAI,IAAI,IAAI,CAACC,gBAAiB,EAC9C;MACE;IACJ;IACA,IAAIC,eAAe,GAAG,IAAI,CAACC,WAAW,CAACT,WAAW,CAAC;IACnD;IACA,IAAIU,aAAa,GAAGC,YAAY,CAACC,OAAO,CAACJ,eAAe,CAAC;IACzDE,aAAa,GAAGG,IAAI,CAACC,KAAK,CAACJ,aAAa,CAAC;IAEzC,SAASK,sBAAsBA,CAACC,WAAW,EAAEC,OAAO,EAAE;MAClD;MACA,IAAIf,OAAO,EAAE;QACT;QACA,KAAK,MAAMgB,QAAQ,IAAID,OAAO,CAACf,OAAO,CAAC,EAAE;UACrC;UACA,IAAIe,OAAO,CAACf,OAAO,CAAC,CAACiB,cAAc,CAACD,QAAQ,CAAC,EAAE;YAAA,IAAAE,YAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,gBAAA;YAC3C,IAAI,GAAAL,YAAA,GAACJ,WAAW,cAAAI,YAAA,uBAAXA,YAAA,CAAclB,OAAO,CAAC,GAAE;cACzBc,WAAW,GAAG;gBACV,GAAGA,WAAW;gBACd,CAACd,OAAO,GAAG,CAAC;cAChB,CAAC;YACL;YACA,IAAI,GAAAmB,aAAA,GAACL,WAAW,cAAAK,aAAA,wBAAAC,qBAAA,GAAXD,aAAA,CAAcnB,OAAO,CAAC,cAAAoB,qBAAA,uBAAtBA,qBAAA,CAAwBJ,QAAQ,GAAE;cAAA,IAAAQ,aAAA;cACnCV,WAAW,GAAG;gBACV,GAAGA,WAAW;gBACd,CAACd,OAAO,GAAG;kBACP,KAAAwB,aAAA,GAAGV,WAAW,cAAAU,aAAA,uBAAXA,aAAA,CAAcxB,OAAO,CAAC;kBACzB,CAACgB,QAAQ,GAAG;gBAChB;cACJ,CAAC;YACL;YACA,MAAMS,YAAY,IAAAJ,aAAA,GAAGP,WAAW,cAAAO,aAAA,wBAAAC,qBAAA,GAAXD,aAAA,CAAcrB,OAAO,CAAC,cAAAsB,qBAAA,uBAAtBA,qBAAA,CAAyBN,QAAQ,CAAC;YACvD,MAAMU,YAAY,GAAGX,OAAO,aAAPA,OAAO,wBAAAQ,gBAAA,GAAPR,OAAO,CAAGf,OAAO,CAAC,cAAAuB,gBAAA,uBAAlBA,gBAAA,CAAqBP,QAAQ,CAAC;YACnD;YACA,IACI,CAAC,CAAAU,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,MAAM,KAAI,CAAC,KACzB,CAAAF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,MAAM,KAAI,CAAC,CAAC,EAC7B;cACEb,WAAW,CAACd,OAAO,CAAC,CAACgB,QAAQ,CAAC,GAAGU,YAAY;cAC7C;YACJ,CAAC,MAAM,IACH,CAAC,CAAAA,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,MAAM,KAAI,CAAC,KACzB,CAAAF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,MAAM,KAAI,CAAC,CAAC,EAC7B;cACEb,WAAW,CAACd,OAAO,CAAC,CAACgB,QAAQ,CAAC,GAC1BS,YAAY,CAACG,MAAM,CAAEC,IAAI,IACrBH,YAAY,CAACI,QAAQ,CAACD,IAAI,CAC9B,CAAC;cACL;YACJ;UACJ;QACJ;MACJ,CAAC,MAAM;QACH,KAAK,MAAMb,QAAQ,IAAID,OAAO,EAAE;UAC5B,IAAIC,QAAQ,IAAIhB,OAAO,EAAE;YACrBc,WAAW,CAACE,QAAQ,CAAC,GAAGD,OAAO,CAACC,QAAQ,CAAC;UAC7C;QACJ;MACJ;MAEA,OAAOF,WAAW;IACtB;IAEA,IAAI,CAACN,aAAa,EAAE;MAChBA,aAAa,GAAG,CAAC,CAAC;IACtB;IAEAN,IAAI,GAAGP,CAAC,CAACoC,KAAK,CAAC7B,IAAI,EAAE,IAAI,CAAC8B,yBAAyB,CAACjC,KAAK,EAAEC,OAAO,CAAC,CAAC;IACpE,MAAMiC,YAAY,GAAGpB,sBAAsB,CAACL,aAAa,EAAEN,IAAI,CAAC;IAChEO,YAAY,CAACyB,OAAO,CAAC5B,eAAe,EAAEK,IAAI,CAACwB,SAAS,CAACF,YAAY,CAAC,CAAC;EACvE,CAAC;EAEDG,UAAU,EAAE,SAAAA,CAAUtC,WAAW,EAAEuC,wBAAwB,GAAG,KAAK,EAAE;IACjE,MAAM;MAAEjC;IAAc,CAAC,GAAGN,WAAW;IAErC,IAAIM,aAAa,IAAI,IAAI,IAAI,CAACiC,wBAAwB,EAAE;MACpD,OAAO,KAAK;IAChB;IAEA,IAAI/B,eAAe,GAAG,IAAI,CAACC,WAAW,CAACT,WAAW,CAAC;IACnD,IAAIwC,SAAS,GAAG7B,YAAY,CAACC,OAAO,CAACJ,eAAe,CAAC;IAErD,IAAIgC,SAAS,EAAE;MACXA,SAAS,GAAG3B,IAAI,CAACC,KAAK,CAAC0B,SAAS,CAAC;IACrC;IAEA,OAAOA,SAAS;EACpB,CAAC;EAEDC,UAAU,EAAE,SAAAA,CAAUzC,WAAW,EAAE;IAC/BW,YAAY,CAAC+B,UAAU,CAAC,IAAI,CAACjC,WAAW,CAACT,WAAW,CAAC,CAAC;EAC1D,CAAC;EAEDS,WAAW,EAAE,SAAAA,CAAUT,WAAW,EAAE;IAChC,IAAI;MAAE2C,QAAQ;MAAEtC;IAAe,CAAC,GAAGL,WAAW;IAC9C,OAAO,kBAAkB2C,QAAQ,IAAItC,cAAc,EAAE,CAAC,CAAC;EAC3D,CAAC;EAEDuC,wBAAwB,EAAE,SAAAA,CAAU5C,WAAW,EAAE;IAC7C,MAAM;MACF6C,cAAc;MACdC,sBAAsB;MACtBC;IACJ,CAAC,GAAG/C,WAAW,CAACgD,KAAK;IACrB;IACA,IAAI5C,IAAI,GAAG,CAAC,CAAC;;IAEb;IACA,SAAS6C,0BAA0BA,CAACC,GAAG,EAAE;MACrC,KAAK,IAAIC,GAAG,IAAID,GAAG,EAAE;QACjB,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAACC,GAAG,CAAC,CAAC,EAAE;UACzBD,GAAG,CAACC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACJ,GAAG,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC;MACJ;MACA,OAAOD,GAAG;IACd;;IAEA;IACA9C,IAAI,CAAC,aAAa,CAAC,GAAG6C,0BAA0B,CAACJ,cAAc,CAAC;IAChEzC,IAAI,CAAC,WAAW,CAAC,GAAG6C,0BAA0B,CAACH,sBAAsB,CAAC;IACtE1C,IAAI,CAAC,cAAc,CAAC,GAAG6C,0BAA0B,CAC7CF,yBACJ,CAAC;IAED,OAAO3C,IAAI;EACf,CAAC;EAED8B,yBAAyB,EAAE,SAAAA,CAAUjC,KAAK,EAAEC,OAAO,EAAE;IACjD;IACA,IAAIE,IAAI,GAAG,CAAC,CAAC;;IAEb;IACA,SAAS6C,0BAA0BA,CAACC,GAAG,EAAE;MACrC,KAAK,IAAIC,GAAG,IAAID,GAAG,EAAE;QACjB,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAACC,GAAG,CAAC,CAAC,EAAE;UACzBD,GAAG,CAACC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACJ,GAAG,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC;MACJ;MACA,OAAOD,GAAG;IACd;;IAEA;IACA9C,IAAI,CAACF,OAAO,CAAC,GAAG+C,0BAA0B,CAAChD,KAAK,CAAC;IACjD,OAAOG,IAAI;EACf;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}