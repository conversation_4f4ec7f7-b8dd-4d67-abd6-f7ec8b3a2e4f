{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\routes\\\\my-tasks\\\\TaskUpdateEditor.js\";\nimport React, { Component } from 'react';\nimport { Modal, Form, Radio, Input, Button, Popover, Steps, message, Row, Col, Avatar, Tooltip, Checkbox, Typography, Upload, Timeline, List, Collapse, Spin, Alert } from 'antd';\nimport FormBuilder from 'antd-form-builder';\nimport http_utils from '../../util/http_utils';\nimport CircularProgress from '../../components/CircularProgress';\nimport { AntDesignOutlined, EllipsisOutlined, InboxOutlined, RightOutlined, ShareAltOutlined, UndoOutlined, UploadOutlined, UserOutlined } from '@ant-design/icons';\n// import ReactTags from 'react-tag-autocomplete'\nimport Dragger from 'antd/lib/upload/Dragger';\nimport RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';\nimport { convertDateFieldsToMoments, convertUTCToDisplayTime, durationAsString, isTimePassed, priorities, getGeneralFileSection, getTouchedFieldsValueInForm, hasAnyFileChanged, isAndroidApp, generateUUID, setRemarkFieldAsNoRemarkIfEmpty, showCreateOrUpdateSuccessMessage } from '../../util/helpers';\nimport TaskSummaryView from '../../components/WIFY/subtasks/TaskSummaryView';\nimport TaskTime from '../../components/WIFY/subtasks/TaskTime';\nimport TimePickerWidget from '../../components/wify-utils/TimePickerWidget';\nimport moment from 'moment';\nimport S3Uploader from '../../components/wify-utils/S3Uploader/S3Uploader';\nimport { decodeFieldsMetaFrmJson, decodeFileSectionsFrmJson, decodeMicSectionsFrmJson, decodeCameraSectionsFrmJson, decodeBLESectionsFrmJson } from '../../components/wify-utils/FieldCreator/helpers';\nimport { getCustomFieldsJsonFrStatus } from './SingleStatusUpdates';\nimport { isArray } from 'lodash';\nimport LocationDetails from './LocationDetails';\nimport { defaultLocationVerificationStatus } from '../../components/WIFY/helpers';\nimport MicInputV2 from '../../components/wify-utils/MicInput_v2';\nimport CameraInput from '../../components/wify-utils/CameraInput';\nimport Countdown from 'antd/lib/statistic/Countdown';\nimport SubtaskCardConfigHelper from '../../components/WIFY/subtasks/SubtaskCardConfigHelper';\nimport _ from 'lodash';\nimport { DraftManager } from './DraftManager';\nimport MyTaskConfigHelper from './MyTaskConfigHelper';\nimport PartsEditor from './PartsEditor';\nimport checkFeatureAccess from '../../util/FeatureAccess';\n\n// import TimelineCard from './TimelineCard';\n// import {Paragraph} from 'antd';\n\nconst protoUrl = '/my-tasks/proto';\nconst submitUrl = '/my-tasks';\n// const otpVerityUrl = \"/my-tasks/otp-verify\";\nconst reSendOtpUrl = '/my-tasks/re-send-otp';\nconst dynamicFormLogicPath = '/my-tasks/exec-dynamic-form-logic';\nlet debouncer;\nfunction disabledDate(current) {\n  // Can not select days before today and today\n  return current && current < moment().subtract(1, 'days').endOf('day');\n}\nclass TaskUpdateEditor extends Component {\n  constructor(props) {\n    super(props);\n    this.initState = {\n      render_helper: false,\n      visible: false,\n      isFormSubmitting: false,\n      viewData: undefined,\n      isLoadingViewData: false,\n      editMode: this.props.editMode,\n      error: '',\n      currentStep: 0,\n      // Attachements and custom files section\n      fileSections: [],\n      filesBySection: {},\n      sectionWiseUploaderReady: {},\n      // Mic sections\n      micSections: [],\n      micRecordingsBySection: {},\n      sectionWiseMicUploaderReady: {},\n      //Camera section\n      cameraSections: [],\n      cameraRecordingsBySection: {},\n      sectionWiseCameraUploaderReady: {},\n      // ---\n      isLoadingLocation: false,\n      locationData: undefined,\n      //otp section\n      otpRegenrated: false,\n      // dynamic form\n      manipulatedDynamicMeta: undefined,\n      isExecutingDynamicFormLogic: false,\n      disableSubmissionButton: false,\n      updateClosureToChildComponent: true,\n      isDynamicFormFirstExec: true,\n      uploadedFilesBySection: {},\n      partsData: [],\n      draftRetryCount: 0 // Track retry attempts for draft application\n    };\n    this.state = this.initState;\n    this.verifyFeatureAccess = async () => {\n      try {\n        let hasAccess = await checkFeatureAccess('TMS250523533568');\n        this.setState({\n          TMS250523533568: hasAccess\n        });\n      } catch (error) {\n        this.setState({\n          TMS250523533568: false\n        });\n        console.error('TaskUpdateEditor :: verifyFeatureAccess :: error : ', error);\n      }\n\n      // Check access for draft functionality feature flag\n      try {\n        let hasDraftAccess = await checkFeatureAccess('TMS250617437781');\n        this.setState({\n          TMS250617437781: hasDraftAccess\n        });\n      } catch (error) {\n        this.setState({\n          TMS250617437781: false\n        });\n        console.error('TaskUpdateEditor :: verifyFeatureAccess :: TMS250617437781 :: error : ', error);\n      }\n    };\n    this.handleOk = () => {\n      this.setState({\n        visible: false,\n        isFormSubmitting: false\n      });\n      this.updateClosureToParent();\n    };\n    this.handleCancel = () => {\n      this.setState({\n        updateClosureToChildComponent: false\n      }, () => {\n        var _this$cameraRef, _this$cameraRef$curre;\n        this.setState({\n          visible: false\n        });\n        this.resetReadyStatusOfCamera();\n        if ((_this$cameraRef = this.cameraRef) === null || _this$cameraRef === void 0 ? void 0 : (_this$cameraRef$curre = _this$cameraRef.current) === null || _this$cameraRef$curre === void 0 ? void 0 : _this$cameraRef$curre.stopCapturing) {\n          this.cameraRef.current.stopCapturing();\n        }\n        this.updateClosureToParent();\n        this.setState({\n          uploadedFilesBySection: {}\n        });\n      });\n    };\n    this.hasAnyBarcodeValueChanged = (form_data, fieldMeta, prefill_data) => {\n      let returnFields = {};\n      fieldMeta.forEach(singleMeta => {\n        if ((form_data === null || form_data === void 0 ? void 0 : form_data[singleMeta.key]) != (prefill_data === null || prefill_data === void 0 ? void 0 : prefill_data[singleMeta.key]) && (singleMeta === null || singleMeta === void 0 ? void 0 : singleMeta.type) == 'Barcode_scanner') {\n          returnFields[singleMeta.key] = form_data[singleMeta.key];\n        }\n      });\n      return returnFields;\n    };\n    this.executeDynamicFormLogic = (changedValues, allValues, isRetryForDraft = false) => {\n      var _this$state, _this$state$viewData, _this$state$viewData$, _this$state2, _this$state2$viewData, _this$state2$viewData2;\n      // Write a API call to TMS here\n      this.setIsExecutingDynamicFormLogic(true);\n      //Todo for dynamic form. Key sent so form can see if draft needs to be rendered\n      let renderingFrmDraft = false;\n      let draftData = null;\n      if ((this.state.isDynamicFormFirstExec || isRetryForDraft) && this.state.TMS250617437781) {\n        // Check if there's draft data available for this dynamic form\n        draftData = DraftManager.applyDraft(this.draftParams(), true);\n        if (draftData && Object.keys(draftData).length > 0) {\n          renderingFrmDraft = true;\n        }\n      }\n      // message.warning(<Spin/>);// To be set by jainish\n\n      allValues['attachments'] = this.state.filesBySection;\n      allValues['mic_files'] = this.state.micRecordingsBySection;\n      allValues['camera_files'] = this.state.cameraRecordingsBySection;\n      const params = {\n        changedValues,\n        allValues,\n        meta: this.getOriginalMetaFrDynamicForm(),\n        currentMeta: this.state.manipulatedDynamicMeta\n      };\n      // console.log('executeDynamicFormLogic params',params);\n      params['sbtsk_type_id'] = this.props.editorItem.sbtsk_type.value;\n      params['srvc_req_id'] = (_this$state = this.state) === null || _this$state === void 0 ? void 0 : (_this$state$viewData = _this$state.viewData) === null || _this$state$viewData === void 0 ? void 0 : (_this$state$viewData$ = _this$state$viewData.form_data) === null || _this$state$viewData$ === void 0 ? void 0 : _this$state$viewData$.srvc_req_id;\n      params['srvc_type_id'] = (_this$state2 = this.state) === null || _this$state2 === void 0 ? void 0 : (_this$state2$viewData = _this$state2.viewData) === null || _this$state2$viewData === void 0 ? void 0 : (_this$state2$viewData2 = _this$state2$viewData.form_data) === null || _this$state2$viewData2 === void 0 ? void 0 : _this$state2$viewData2.srvc_type_id;\n      params['renderingFrmDraft'] = renderingFrmDraft;\n      http_utils.performPostCall(dynamicFormLogicPath + '/' + this.props.editorItem.id + '/' + this.props.updateTypeId, params, resp => {\n        // console.log('executeDynamicFormLogic resp.data', resp.data);\n        const {\n          meta,\n          allValues,\n          changedValues,\n          manipulatedFieldValues,\n          disableFormSubmissionButton,\n          errorMessage\n        } = resp.data.data;\n        if (errorMessage) {\n          message.config({\n            duration: errorMessage.duration || 3\n          });\n          message.error(errorMessage.message);\n        }\n        this.setState({\n          manipulatedDynamicMeta: meta,\n          disableSubmissionButton: disableFormSubmissionButton\n        }, () => {\n          // For files,mic,camera\n          this.initConfigData({\n            data: this.state.viewData\n          }, () => {\n            this.formRef.current.setFieldsValue(manipulatedFieldValues);\n\n            // Apply draft data and retry if needed for dynamic forms\n            if (renderingFrmDraft && draftData && this.state.TMS250617437781) {\n              this.applyDraftAndRetryIfNeeded(draftData, isRetryForDraft);\n            } else {\n              this.setIsExecutingDynamicFormLogic(false);\n              if (!this.state.isDynamicFormFirstExec && !isRetryForDraft && this.state.TMS250617437781) {\n                DraftManager.updateDraft(this.draftParams());\n              } else {\n                this.setState({\n                  isDynamicFormFirstExec: false\n                });\n              }\n            }\n          }, true);\n        });\n      }, error => {\n        console.log('Error in onFormValueChange API call', http_utils.decodeErrorToMessage(error));\n        message.error('Auto form change not working!, Contact admin ');\n        this.setIsExecutingDynamicFormLogic(false);\n      });\n    };\n    this.onFormValueChanged = (changedValues, allValues) => {\n      if (debouncer) {\n        clearTimeout(debouncer);\n      }\n      debouncer = setTimeout(() => {\n        this.executeDynamicFormLogic(changedValues, allValues);\n      }, 750);\n    };\n    this.onCustomButtonClick = fieldId => {\n      if (this.isDynamicForm()) {\n        var _this$formRef, _this$formRef$current;\n        this.onFormValueChanged({\n          [fieldId]: true\n        }, (_this$formRef = this.formRef) === null || _this$formRef === void 0 ? void 0 : (_this$formRef$current = _this$formRef.current) === null || _this$formRef$current === void 0 ? void 0 : _this$formRef$current.getFieldsValue());\n      } else {\n        console.log('Not a dynamic form');\n      }\n    };\n    this.submitForm = data => {\n      var _this$formRef2, _this$state$viewData2, _this$state$viewData3, _this$state3, _this$state3$viewData, _this$state3$viewData2, _this$state4, _this$state4$viewData, _this$state4$viewData2;\n      this.setState({\n        isFormSubmitting: true\n      });\n      let touchedFields = getTouchedFieldsValueInForm(data, (_this$formRef2 = this.formRef) === null || _this$formRef2 === void 0 ? void 0 : _this$formRef2.current, this.getSpecificFieldsFrStatus().fields);\n      touchedFields = {\n        ...touchedFields,\n        ...this.hasAnyBarcodeValueChanged(data, this.getSpecificFieldsFrStatus().fields, (_this$state$viewData2 = this.state.viewData) === null || _this$state$viewData2 === void 0 ? void 0 : (_this$state$viewData3 = _this$state$viewData2.form_data) === null || _this$state$viewData3 === void 0 ? void 0 : _this$state$viewData3.form_data)\n      };\n      if (this.state.editMode) {\n        var _this$state$viewData4, _this$state$viewData5, _this$state$viewData6, _this$state$viewData7, _this$state$viewData8, _this$state$viewData9, _this$state$viewData0, _this$state$viewData1, _this$state$viewData10;\n        // compare files with prefill data\n        let isAnyFileChanged = hasAnyFileChanged(this.state.filesBySection, (_this$state$viewData4 = this.state.viewData) === null || _this$state$viewData4 === void 0 ? void 0 : (_this$state$viewData5 = _this$state$viewData4.form_data) === null || _this$state$viewData5 === void 0 ? void 0 : (_this$state$viewData6 = _this$state$viewData5.form_data) === null || _this$state$viewData6 === void 0 ? void 0 : _this$state$viewData6.attachments);\n        if (isAnyFileChanged) {\n          touchedFields['attachments'] = this.state.filesBySection;\n        }\n\n        // compare files with prefill data\n        let isAnyMicFileChanged = hasAnyFileChanged(this.state.micRecordingsBySection, (_this$state$viewData7 = this.state.viewData) === null || _this$state$viewData7 === void 0 ? void 0 : (_this$state$viewData8 = _this$state$viewData7.form_data) === null || _this$state$viewData8 === void 0 ? void 0 : (_this$state$viewData9 = _this$state$viewData8.form_data) === null || _this$state$viewData9 === void 0 ? void 0 : _this$state$viewData9.mic_files);\n        if (isAnyMicFileChanged) {\n          touchedFields['mic_files'] = this.state.micRecordingsBySection;\n        }\n\n        // compare camera files with prefill data\n        let isAnyCameraFileChanged = hasAnyFileChanged(this.state.cameraRecordingsBySection, (_this$state$viewData0 = this.state.viewData) === null || _this$state$viewData0 === void 0 ? void 0 : (_this$state$viewData1 = _this$state$viewData0.form_data) === null || _this$state$viewData1 === void 0 ? void 0 : (_this$state$viewData10 = _this$state$viewData1.form_data) === null || _this$state$viewData10 === void 0 ? void 0 : _this$state$viewData10.camera_files);\n        if (isAnyCameraFileChanged) {\n          touchedFields['camera_files'] = this.state.cameraRecordingsBySection;\n        }\n        touchedFields['sbtsk_parts_consumption'] = this.state.partsData;\n      } else {\n        data['attachments'] = this.state.filesBySection;\n        data['mic_files'] = this.state.micRecordingsBySection;\n        data['camera_files'] = this.state.cameraRecordingsBySection;\n        data['sbtsk_parts_consumption'] = this.state.partsData;\n      }\n      if (this.state.TMS250617437781) {\n        let draftData = DraftManager.applyDraft(this.draftParams(), this.isDynamicForm());\n        if (draftData) {\n          touchedFields = _.merge(touchedFields, draftData);\n        }\n      }\n      var params = this.state.editMode ? touchedFields : data;\n      if (this.isDynamicForm()) {\n        data['attachments'] = this.state.filesBySection;\n        data['mic_files'] = this.state.micRecordingsBySection;\n        data['camera_files'] = this.state.cameraRecordingsBySection;\n        params = data;\n      }\n      if (Object.keys(params).length == 0) {\n        var _this$state$viewData11;\n        //if task updating with that updatetypeid which configured for multiple trigger then allow to update even nothing chnaged in form fields\n        const canTriggerUpdateWithoutChanges = MyTaskConfigHelper.statusesToAllowFrMultipleTrigger((_this$state$viewData11 = this.state.viewData) === null || _this$state$viewData11 === void 0 ? void 0 : _this$state$viewData11.sbtsk_config_data[0], this.props.updateTypeId);\n        if (!canTriggerUpdateWithoutChanges) {\n          this.setState({\n            isFormSubmitting: false\n          }, () => {\n            message.info('No change in form');\n          });\n          // Nothing to submit\n          return;\n        }\n      }\n      params['sbtsk_type_id'] = this.props.editorItem.sbtsk_type.value;\n      params['srvc_req_id'] = (_this$state3 = this.state) === null || _this$state3 === void 0 ? void 0 : (_this$state3$viewData = _this$state3.viewData) === null || _this$state3$viewData === void 0 ? void 0 : (_this$state3$viewData2 = _this$state3$viewData.form_data) === null || _this$state3$viewData2 === void 0 ? void 0 : _this$state3$viewData2.srvc_req_id;\n      params['srvc_type_id'] = (_this$state4 = this.state) === null || _this$state4 === void 0 ? void 0 : (_this$state4$viewData = _this$state4.viewData) === null || _this$state4$viewData === void 0 ? void 0 : (_this$state4$viewData2 = _this$state4$viewData.form_data) === null || _this$state4$viewData2 === void 0 ? void 0 : _this$state4$viewData2.srvc_type_id;\n      if (this.needToCaptureLocation()) {\n        var _this$state$locationD, _this$state$locationD2;\n        params['user_gps_location_details'] = this.state.locationData;\n        params['user_gps_location_text'] = (_this$state$locationD = this.state.locationData) === null || _this$state$locationD === void 0 ? void 0 : (_this$state$locationD2 = _this$state$locationD.address) === null || _this$state$locationD2 === void 0 ? void 0 : _this$state$locationD2.text;\n        params['user_gps_location_status'] = defaultLocationVerificationStatus;\n      }\n      const onComplete = resp => {\n        if (this.state.TMS250617437781) {\n          DraftManager.clearDraft(this.draftParams());\n        }\n        this.setState({\n          isFormSubmitting: false,\n          error: '',\n          visible: false\n        });\n        this.tellParentToRefreshList(resp.entry_id);\n        this.updateClosureToParent();\n        showCreateOrUpdateSuccessMessage();\n      };\n      const onError = error => {\n        // compare statuses here\n        this.setState({\n          isFormSubmitting: false,\n          error: http_utils.decodeErrorToMessage(error)\n        });\n      };\n      http_utils.performPutCall(submitUrl + '/' + this.props.editorItem.id + '/' + this.props.updateTypeId, params, onComplete, onError);\n    };\n    this.handlePartsUpdate = updatedItems => {\n      this.setState({\n        partsData: updatedItems\n      });\n\n      // DraftManager.updateDraft(\n      //     this.draftParams(),\n      //     //this.state.partsData,\n      //     updatedItems,\n      //     'sbtsk_parts_consumption'\n      // );\n    };\n    this.draftParams = () => {\n      var _this$props, _this$props$editorIte, _this$props2, _this$formRef3, _this$formRef3$curren;\n      return {\n        sbtsk_id: (_this$props = this.props) === null || _this$props === void 0 ? void 0 : (_this$props$editorIte = _this$props.editorItem) === null || _this$props$editorIte === void 0 ? void 0 : _this$props$editorIte.id,\n        update_type_id: (_this$props2 = this.props) === null || _this$props2 === void 0 ? void 0 : _this$props2.updateTypeId,\n        state: {\n          filesBySection: this.state.filesBySection,\n          micRecordingsBySection: this.state.micRecordingsBySection,\n          cameraRecordingsBySection: this.state.cameraRecordingsBySection\n        },\n        fieldValues: (_this$formRef3 = this.formRef) === null || _this$formRef3 === void 0 ? void 0 : (_this$formRef3$curren = _this$formRef3.current) === null || _this$formRef3$curren === void 0 ? void 0 : _this$formRef3$curren.getFieldsValue(),\n        isDynamicForm: this.isDynamicForm()\n      };\n    };\n    this.removeUploadedFilesFromDraft = (draft, uploadedFiles) => {\n      const newDraft = JSON.parse(JSON.stringify(draft)); // Create a deep copy of draft\n\n      for (const key in newDraft.attachments) {\n        if (newDraft.attachments.hasOwnProperty(key)) {\n          newDraft.attachments[key] = newDraft.attachments[key].filter(file => {\n            var _uploadedFiles$key;\n            return !((_uploadedFiles$key = uploadedFiles[key]) === null || _uploadedFiles$key === void 0 ? void 0 : _uploadedFiles$key.includes(file));\n          });\n        }\n      }\n      return newDraft;\n    };\n    this.formRef = React.createRef();\n    this.cameraRef = React.createRef();\n    // console.log('Rxd props task update editor',props);\n  }\n  componentDidMount() {\n    this.initViewData();\n    this.verifyFeatureAccess();\n  }\n  initGeoFenceStuff() {\n    // console.log('CDM');\n    // console.log('props',this.props);\n    // console.log('config_data',this.props.updateTypeDetails?.config_data);\n\n    // message.success(\n    //     isAndroidApp() ?\n    //     'Is android app' :\n    //     'Not android app'\n    // )\n\n    // console.log('isArray ',isArray(geo_verification_enabled_statuses))\n    // console.log('update_status_key',update_status_key);\n    if (this.needToCaptureLocation()) {\n      this.setState({\n        isLoadingLocation: true\n      });\n      this.getGPSLocationFrmDevice();\n      // console.log('Check if Geo verification is enabled for the status & whether this is android App passed');\n    }\n  }\n  needToCaptureLocation() {\n    var _this$props$updateTyp, _this$props$updateTyp2;\n    const update_status_key = this.props.updateTypeId;\n    const geo_verification_enabled_statuses = (_this$props$updateTyp = this.props.updateTypeDetails) === null || _this$props$updateTyp === void 0 ? void 0 : (_this$props$updateTyp2 = _this$props$updateTyp.config_data) === null || _this$props$updateTyp2 === void 0 ? void 0 : _this$props$updateTyp2.enable_geo_verification_for;\n    return isArray(geo_verification_enabled_statuses) && geo_verification_enabled_statuses.includes(update_status_key);\n    // TODO uncomment before push && isAndroidApp()\n  }\n  getGPSLocationFrmDevice() {\n    return new Promise((resolve, reject) => {\n      var _window$wifyApp;\n      console.log('gpsLocation', 'Getting GPS location..');\n      if ((_window$wifyApp = window.wifyApp) === null || _window$wifyApp === void 0 ? void 0 : _window$wifyApp.getGPSLocation) {\n        var _window$wifyApp2;\n        const locationRequestEventName = 'location_request_' + generateUUID();\n        window.addEventListener(locationRequestEventName, e => {\n          const locationData = e.detail;\n          setTimeout(() => {\n            this.recivedLocationData(locationData);\n          }, 1000);\n\n          // console.log('Received gpsLocation from android -',JSON.stringify(e.detail));\n        });\n        const appCallResp = (_window$wifyApp2 = window.wifyApp) === null || _window$wifyApp2 === void 0 ? void 0 : _window$wifyApp2.getGPSLocation(locationRequestEventName);\n        resolve();\n      } else {\n        // const dummyLocationData = {\"latitude\":19.2181636,\"longitude\":72.8596517,\"isMock\":false,\"timestamp\":1653559234,\"speed\":0,\"bearing\":0,\"altitude\":-47,\"accuracy\":49.86800003051758,\"address\":{\"text\":\"RAJNANDHAN CHS A-D, SAMVED CO-OPERATIVE HOUSING SOCIETY SAMVEL CHS, राजेंद्र नगर, बोरिवली ईस्ट, मुंबई, महाराष्ट्र 400066, India\",\"area\":\"महाराष्ट्र\",\"locality\":\"मुंबई\",\"postalCode\":\"400066\"},\"device_info\":{\"device_unique_id\":\"0e7ca81e10524a85\",\"device_android_version\":\"Android v10.0, API Level: 29\",\"device_mode_name\":\"HMD Global Nokia 6.1\",\"battery\":96,\"battery_acc\":false}}\n        // console.log('Loaded dummy location data');\n        message.success('Please update your app');\n        // setTimeout(()=>{\n        //     this.recivedLocationData(dummyLocationData);\n        // },1000)\n        resolve();\n      }\n    });\n  }\n  recivedLocationData(locationData) {\n    this.setState({\n      isLoadingLocation: false,\n      locationData: locationData\n    });\n  }\n  initViewData() {\n    // console.log(\"Trying to init view Data\",this.state.visible);\n    this.initGeoFenceStuff();\n    if (this.state.editMode && this.state.visible || !this.state.editMode && this.state.viewData == undefined && !this.state.isLoadingViewData) {\n      var _this$props$editorIte2;\n      this.setState({\n        isLoadingViewData: true\n      });\n      var params = {};\n      params['sbtsk_type_id'] = '' + this.props.editorItem.sbtsk_type.value;\n      params['srvcReqId'] = (_this$props$editorIte2 = this.props.editorItem.srvc_req_details) === null || _this$props$editorIte2 === void 0 ? void 0 : _this$props$editorIte2.id;\n      const onComplete = resp => {\n        this.initConfigData(resp,\n        // then set loading false\n        this.setState({\n          isLoadingViewData: false,\n          viewData: resp.data,\n          error: ''\n        }, () => {\n          if (this.isDynamicForm()) {\n            this.onFormValueChanged({}, {}); // CDM\n          }\n        }));\n      };\n      const onError = error => {\n        // console.log(error.response.status);\n        this.setState({\n          isLoadingViewData: false,\n          error: http_utils.decodeErrorToMessage(error)\n        });\n      };\n      var url = protoUrl + '/' + this.props.editorItem.id + '/' + this.props.updateTypeId;\n      // console.log(url);\n      http_utils.performGetCall(url, params, onComplete, onError);\n    }\n  }\n  initConfigData(entry_specific_data, then, dont_override_files = false) {\n    var _this$props$updateTyp3, _entry_specific_data$, _entry_specific_data$3, _entry_specific_data$5;\n    let isGeneralFileSectionMandatory = SubtaskCardConfigHelper.isDefaultAttachmentFieldMandatory((_this$props$updateTyp3 = this.props.updateTypeDetails) === null || _this$props$updateTyp3 === void 0 ? void 0 : _this$props$updateTyp3.config_data, this.props.updateTypeId);\n    let newFileSections = [getGeneralFileSection(isGeneralFileSectionMandatory)];\n    let custom_file_sections = this.getCustomFileSectionsFrmConfig();\n    if (custom_file_sections && custom_file_sections.length > 0) {\n      var _this$props$updateTyp4, _this$props$updateTyp5;\n      const showAttachMentsAtBottom = (_this$props$updateTyp4 = this.props.updateTypeDetails) === null || _this$props$updateTyp4 === void 0 ? void 0 : (_this$props$updateTyp5 = _this$props$updateTyp4.config_data) === null || _this$props$updateTyp5 === void 0 ? void 0 : _this$props$updateTyp5[`move_attachments_field_to_bottom_for_${this.props.updateTypeId}`];\n      newFileSections = showAttachMentsAtBottom ? [...custom_file_sections, ...newFileSections] : [...newFileSections, ...custom_file_sections];\n    }\n    // console.log('newFileSections',newFileSections)\n\n    // Prefilling attachments\n    let initialFilesBySection = dont_override_files ? this.state.filesBySection : {};\n    if ((_entry_specific_data$ = entry_specific_data.data.form_data) === null || _entry_specific_data$ === void 0 ? void 0 : _entry_specific_data$.form_data) {\n      var _entry_specific_data$2;\n      initialFilesBySection = (_entry_specific_data$2 = entry_specific_data.data.form_data) === null || _entry_specific_data$2 === void 0 ? void 0 : _entry_specific_data$2.form_data['attachments'];\n    }\n    let newMicSections = [];\n    let customMicSections = this.getCustomMicSectionsFrmConfig();\n    if (customMicSections && customMicSections.length > 0) {\n      newMicSections = [...newMicSections, ...customMicSections];\n    }\n    let initialMicRecordingsBySection = dont_override_files ? this.state.micRecordingsBySection : {};\n    if ((_entry_specific_data$3 = entry_specific_data.data.form_data) === null || _entry_specific_data$3 === void 0 ? void 0 : _entry_specific_data$3.form_data) {\n      var _entry_specific_data$4;\n      initialMicRecordingsBySection = (_entry_specific_data$4 = entry_specific_data.data.form_data) === null || _entry_specific_data$4 === void 0 ? void 0 : _entry_specific_data$4.form_data['mic_files'];\n    }\n    let newCameraSections = [];\n    let customCameraSections = this.getCustomCameraSectionsFrmConfig();\n    if (customCameraSections && customCameraSections.length > 0) {\n      newCameraSections = [...newCameraSections, ...customCameraSections];\n    }\n    let initialCameraRecordingsBySection = dont_override_files ? this.state.cameraRecordingsBySection : {};\n    if ((_entry_specific_data$5 = entry_specific_data.data.form_data) === null || _entry_specific_data$5 === void 0 ? void 0 : _entry_specific_data$5.form_data) {\n      var _entry_specific_data$6;\n      initialCameraRecordingsBySection = (_entry_specific_data$6 = entry_specific_data.data.form_data) === null || _entry_specific_data$6 === void 0 ? void 0 : _entry_specific_data$6.form_data['camera_files'];\n    }\n    this.setState({\n      fileSections: newFileSections,\n      filesBySection: {\n        ...initialFilesBySection\n      },\n      micSections: newMicSections,\n      micRecordingsBySection: {\n        ...initialMicRecordingsBySection\n      },\n      cameraSections: newCameraSections,\n      cameraRecordingsBySection: {\n        ...initialCameraRecordingsBySection\n      }\n    }, then);\n  }\n  getCustomMicSectionsFrmConfig() {\n    return decodeMicSectionsFrmJson(this.getCustomFieldsJsonFrmConfig());\n  }\n  getCustomCameraSectionsFrmConfig() {\n    return decodeCameraSectionsFrmJson(this.getCustomFieldsJsonFrmConfig());\n  }\n  getCustomFileSectionsFrmConfig() {\n    return decodeFileSectionsFrmJson(this.getCustomFieldsJsonFrmConfig());\n  }\n  componentDidUpdate(prevProps, prevState) {\n    if (prevProps.editorItem != this.props.editorItem || prevProps.showEditor != this.props.showEditor) {\n      this.setState({\n        render_helper: !this.state.render_helper,\n        visible: this.props.showEditor,\n        draftRetryCount: 0 // Reset retry counter when editor opens\n      }, function () {\n        if (this.props.showEditor && this.state.editMode) {\n          this.initViewData();\n        }\n      });\n    } else {\n      if (this.state.refreshOnUpdate) {\n        this.setState({\n          refreshOnUpdate: false\n        }, this.initViewData());\n      }\n    }\n  }\n  updateClosureToParent() {\n    if (this.props.onClose != undefined) {\n      this.props.onClose();\n    }\n    this.setState({\n      refreshOnUpdate: true,\n      ...this.initState\n    });\n  }\n  resetReadyStatusOfCamera() {\n    Object.keys(this.state.sectionWiseCameraUploaderReady).forEach(singleKey => this.state.sectionWiseCameraUploaderReady[singleKey] = true);\n  }\n  tellParentToRefreshList(entry_id) {\n    // console.log(\"Trying to to tell parent to refresh list\");\n    if (this.props.onDataModified != undefined) {\n      this.props.onDataModified(entry_id);\n    }\n  }\n  showDraftSavedPopUp() {\n    message.info({\n      style: {\n        marginTop: '50vh'\n      },\n      content: 'Saved as draft'\n    }, 3000);\n  }\n  mandatoryStatusRequirement(reqStatusArray) {\n    let statusToBeCompleted = [];\n    this.props.updateTypeDetails.statuses.forEach(singleStatus => {\n      if (reqStatusArray.includes(singleStatus.value)) {\n        statusToBeCompleted.push(singleStatus.title);\n      }\n    });\n    return statusToBeCompleted.join(', ');\n  }\n  getOriginalMetaFrDynamicForm() {\n    var _this$props$updateTyp6;\n    let custFieldJson = getCustomFieldsJsonFrStatus(this.props.updateTypeId, (_this$props$updateTyp6 = this.props.updateTypeDetails) === null || _this$props$updateTyp6 === void 0 ? void 0 : _this$props$updateTyp6.config_data);\n    if (custFieldJson) {\n      var _JSON$parse;\n      custFieldJson = (_JSON$parse = JSON.parse(custFieldJson)) === null || _JSON$parse === void 0 ? void 0 : _JSON$parse.translatedFields;\n      // custFieldJson = decodeFieldsMetaFrmJson(custFieldJson,undefined,true,true,this.formRef);\n    }\n    return custFieldJson;\n  }\n  setIsExecutingDynamicFormLogic(value) {\n    this.setState({\n      isExecutingDynamicFormLogic: value\n    });\n  }\n\n  // Helper method to check which draft fields are missing from current form values\n  getMissingDraftFields(draftData, currentFormValues) {\n    if (!draftData || !currentFormValues) return {};\n    const missingFields = {};\n    Object.keys(draftData).forEach(key => {\n      // Skip file-related fields as they are handled separately\n      if (['attachments', 'mic_files', 'camera_files'].includes(key)) {\n        return;\n      }\n      const draftValue = draftData[key];\n      const currentValue = currentFormValues[key];\n\n      // Check if the field is missing or has different value\n      if (draftValue && !currentValue) {\n        missingFields[key] = draftValue;\n      }\n    });\n    return missingFields;\n  }\n\n  // Apply draft data and retry dynamic form logic if needed\n  applyDraftAndRetryIfNeeded(draftData, isRetryForDraft = false) {\n    // Check if draft functionality is enabled\n    if (!this.state.TMS250617437781) {\n      this.finalizeDraftApplication();\n      return;\n    }\n    const MAX_RETRY_ATTEMPTS = 5; // Prevent infinite loops\n    const currentFormValues = this.formRef.current.getFieldsValue();\n    console.log('currentFormValues', currentFormValues);\n    const missingFields = this.getMissingDraftFields(draftData, currentFormValues);\n    console.log('missingFields', missingFields);\n    if (Object.keys(missingFields).length > 0 && this.state.draftRetryCount < MAX_RETRY_ATTEMPTS) {\n      // Apply missing draft fields to the form\n      this.formRef.current.setFieldsValue(missingFields);\n\n      // Get updated form values after applying missing fields\n      const updatedFormValues = this.formRef.current.getFieldsValue();\n      console.log('updatedFormValues', updatedFormValues);\n\n      // Check if we still have missing fields after this application\n      const stillMissingFields = this.getMissingDraftFields(draftData, updatedFormValues);\n      console.log('stillMissingFields', stillMissingFields);\n      if (Object.keys(stillMissingFields).length > 0) {\n        // We made progress, retry the dynamic form logic\n        console.log('Retrying dynamic form logic to apply remaining draft fields:', stillMissingFields, 'Attempt:', this.state.draftRetryCount + 1);\n        this.setState({\n          draftRetryCount: this.state.draftRetryCount + 1\n        });\n        setTimeout(() => {\n          this.executeDynamicFormLogic({}, updatedFormValues, true);\n        }, 100);\n      } else {\n        // No more progress can be made or all fields applied\n        this.finalizeDraftApplication();\n      }\n    } else {\n      // All draft fields are applied or max retries reached\n      this.finalizeDraftApplication();\n    }\n  }\n\n  // Finalize draft application and reset state\n  finalizeDraftApplication() {\n    this.setIsExecutingDynamicFormLogic(false);\n    this.setState({\n      isDynamicFormFirstExec: false,\n      draftRetryCount: 0 // Reset retry counter\n    });\n  }\n  createParamsFrBleExec() {\n    var _this$props$editorIte3, _this$props$editorIte4, _this$state5, _this$state5$viewData, _this$state5$viewData2, _this$state6, _this$state6$viewData, _this$state6$viewData2, _this$props$editorIte5, _this$props3, _this$state7, _this$state7$viewData, _this$state7$viewData2;\n    return {\n      sbtsk_type_id: (_this$props$editorIte3 = this.props.editorItem) === null || _this$props$editorIte3 === void 0 ? void 0 : (_this$props$editorIte4 = _this$props$editorIte3.sbtsk_type) === null || _this$props$editorIte4 === void 0 ? void 0 : _this$props$editorIte4.value,\n      srvc_req_id: (_this$state5 = this.state) === null || _this$state5 === void 0 ? void 0 : (_this$state5$viewData = _this$state5.viewData) === null || _this$state5$viewData === void 0 ? void 0 : (_this$state5$viewData2 = _this$state5$viewData.form_data) === null || _this$state5$viewData2 === void 0 ? void 0 : _this$state5$viewData2.srvc_req_id,\n      srvc_type_id: (_this$state6 = this.state) === null || _this$state6 === void 0 ? void 0 : (_this$state6$viewData = _this$state6.viewData) === null || _this$state6$viewData === void 0 ? void 0 : (_this$state6$viewData2 = _this$state6$viewData.form_data) === null || _this$state6$viewData2 === void 0 ? void 0 : _this$state6$viewData2.srvc_type_id,\n      entry_id: (_this$props$editorIte5 = this.props.editorItem) === null || _this$props$editorIte5 === void 0 ? void 0 : _this$props$editorIte5.id,\n      update_type_id: (_this$props3 = this.props) === null || _this$props3 === void 0 ? void 0 : _this$props3.updateTypeId,\n      collab_order_id: (_this$state7 = this.state) === null || _this$state7 === void 0 ? void 0 : (_this$state7$viewData = _this$state7.viewData) === null || _this$state7$viewData === void 0 ? void 0 : (_this$state7$viewData2 = _this$state7$viewData.form_data) === null || _this$state7$viewData2 === void 0 ? void 0 : _this$state7$viewData2.collab_order_id\n    };\n  }\n  bleComponentProps() {\n    return {\n      taskUpdateVisible: this.state.updateClosureToChildComponent,\n      paramsFrLambdaExec: this.createParamsFrBleExec()\n    };\n  }\n  reGenerateOtp() {\n    var _this$state8, _this$state8$viewData, _this$state8$viewData2;\n    this.setState({\n      isFormSubmitting: true,\n      otpRegenrated: true,\n      otp_expiry: Math.floor(Date.now()) + 30000\n    });\n    let params = {};\n    params['reSendOtp'] = true;\n    params['sbtsk_db_id'] = this.props.editorItem.id;\n    const onComplete = resp => {\n      this.setState({\n        isFormSubmitting: false\n      });\n    };\n    const onError = error => {\n      // compare statuses here\n      this.setState({\n        isFormSubmitting: false,\n        error: http_utils.decodeErrorToMessage(error)\n      });\n    };\n    http_utils.performGetCall(reSendOtpUrl + '/' + ((_this$state8 = this.state) === null || _this$state8 === void 0 ? void 0 : (_this$state8$viewData = _this$state8.viewData) === null || _this$state8$viewData === void 0 ? void 0 : (_this$state8$viewData2 = _this$state8$viewData.form_data) === null || _this$state8$viewData2 === void 0 ? void 0 : _this$state8$viewData2.srvc_req_id), params, onComplete, onError);\n  }\n  onOTPCountDownFinish() {\n    this.setState({\n      render_helper: !this.state.render_helper\n    });\n  }\n  getRemarkFieldFrMeta(configuredColSpan) {\n    return {\n      key: `remarks`,\n      colSpan: configuredColSpan,\n      label: 'Remarks',\n      widget: 'textarea',\n      rules: [{\n        required: true\n      }]\n    };\n  }\n  getRequestInfoMeta() {\n    var _this$formRef$current2, _this$state$viewData12, _this$state$viewData13, _this$state$viewData14, _this$state$viewData15, _this$state$viewData16, _this$state$viewData17, _this$state9, _this$state9$viewData, _this$state9$viewData2, _this$state0, _this$state0$viewData, _this$state0$viewData2, _this$state1, _this$state1$viewData, _this$state1$viewData2, _this$state10, _this$state10$viewDat, _this$state10$viewDat2, _this$state11, _this$state11$viewDat, _this$state11$viewDat2, _this$props4, _this$props4$editorIt, _this$state$viewData18, _this$state$viewData19, _this$state$viewData20, _this$props$updateTyp7, _this$props$updateTyp8, _this$props$updateTyp9, _this$props$updateTyp0;\n    const startTimeFrEndTime = (_this$formRef$current2 = this.formRef.current) === null || _this$formRef$current2 === void 0 ? void 0 : _this$formRef$current2.getFieldValue('sbtsk_start_time');\n    const startOfDay = ((_this$state$viewData12 = this.state.viewData) === null || _this$state$viewData12 === void 0 ? void 0 : (_this$state$viewData13 = _this$state$viewData12.sbtsk_config_data[0]) === null || _this$state$viewData13 === void 0 ? void 0 : (_this$state$viewData14 = _this$state$viewData13.config_data) === null || _this$state$viewData14 === void 0 ? void 0 : _this$state$viewData14.sbtsk_time_slot_lower_limit) || '09:00AM';\n    const endOfDay = ((_this$state$viewData15 = this.state.viewData) === null || _this$state$viewData15 === void 0 ? void 0 : (_this$state$viewData16 = _this$state$viewData15.sbtsk_config_data[0]) === null || _this$state$viewData16 === void 0 ? void 0 : (_this$state$viewData17 = _this$state$viewData16.config_data) === null || _this$state$viewData17 === void 0 ? void 0 : _this$state$viewData17.sbtsk_time_slot_upper_limit) || '07:00PM';\n    let showOtpField = (_this$state9 = this.state) === null || _this$state9 === void 0 ? void 0 : (_this$state9$viewData = _this$state9.viewData) === null || _this$state9$viewData === void 0 ? void 0 : (_this$state9$viewData2 = _this$state9$viewData.form_data) === null || _this$state9$viewData2 === void 0 ? void 0 : _this$state9$viewData2.configure_consumer_otp_verification;\n    let doSrvcReqHasOtpInFormData = (_this$state0 = this.state) === null || _this$state0 === void 0 ? void 0 : (_this$state0$viewData = _this$state0.viewData) === null || _this$state0$viewData === void 0 ? void 0 : (_this$state0$viewData2 = _this$state0$viewData.form_data) === null || _this$state0$viewData2 === void 0 ? void 0 : _this$state0$viewData2.srvc_req_has_otp;\n    let seletedStatusForOtpVerification = (_this$state1 = this.state) === null || _this$state1 === void 0 ? void 0 : (_this$state1$viewData = _this$state1.viewData) === null || _this$state1$viewData === void 0 ? void 0 : (_this$state1$viewData2 = _this$state1$viewData.form_data) === null || _this$state1$viewData2 === void 0 ? void 0 : _this$state1$viewData2.selected_status_for_otp_verification;\n    let selectedSrvcTypeIdForOtpVerification = (_this$state10 = this.state) === null || _this$state10 === void 0 ? void 0 : (_this$state10$viewDat = _this$state10.viewData) === null || _this$state10$viewDat === void 0 ? void 0 : (_this$state10$viewDat2 = _this$state10$viewDat.form_data) === null || _this$state10$viewDat2 === void 0 ? void 0 : _this$state10$viewDat2.selected_srvc_type_id_for_otp_verification;\n    let srvc_type_id_of_sbtsk = (_this$state11 = this.state) === null || _this$state11 === void 0 ? void 0 : (_this$state11$viewDat = _this$state11.viewData) === null || _this$state11$viewDat === void 0 ? void 0 : (_this$state11$viewDat2 = _this$state11$viewDat.form_data) === null || _this$state11$viewDat2 === void 0 ? void 0 : _this$state11$viewDat2.srvc_type_id;\n    let otp_field_label = ((_this$props4 = this.props) === null || _this$props4 === void 0 ? void 0 : (_this$props4$editorIt = _this$props4.editorItem) === null || _this$props4$editorIt === void 0 ? void 0 : _this$props4$editorIt.otp_field_label) || 'OTP';\n    const updateTypeDetails = this.getUpdateTypeDetails();\n    const statusConfiguredFrParts = ((_this$state$viewData18 = this.state.viewData) === null || _this$state$viewData18 === void 0 ? void 0 : (_this$state$viewData19 = _this$state$viewData18.sbtsk_config_data[0]) === null || _this$state$viewData19 === void 0 ? void 0 : (_this$state$viewData20 = _this$state$viewData19.config_data) === null || _this$state$viewData20 === void 0 ? void 0 : _this$state$viewData20.statues_for_parts_consumption) || [];\n    let isStatusConfiguredFrParts = false;\n    if (statusConfiguredFrParts.includes(this.props.updateTypeId)) {\n      isStatusConfiguredFrParts = true;\n    }\n    const postponeFields = [{\n      key: 'sbtsk_start_day',\n      label: /*#__PURE__*/React.createElement(\"b\", {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 21\n        }\n      }, /*#__PURE__*/React.createElement(\"i\", {\n        className: \"icon icon-calendar gx-mr-2\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 992,\n          columnNumber: 25\n        }\n      }), \"Start date\"),\n      widget: 'date-picker',\n      required: true,\n      colSpan: 3,\n      widgetProps: {\n        disabledDate: disabledDate,\n        style: {\n          width: '100%'\n        },\n        onChange: (value, dateString) => {\n          this.formRef.current.setFieldsValue({\n            sbtsk_start_day: moment.utc(dateString)\n          });\n        }\n      }\n    }, {\n      key: 'label2',\n      colSpan: 4,\n      render() {\n        return /*#__PURE__*/React.createElement(React.Fragment, null);\n      }\n    }, {\n      key: 'sbtsk_start_time',\n      label: /*#__PURE__*/React.createElement(\"b\", {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1020,\n          columnNumber: 21\n        }\n      }, /*#__PURE__*/React.createElement(\"i\", {\n        className: \"icon icon-timepicker gx-mr-2\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1021,\n          columnNumber: 25\n        }\n      }), \"Start Time\"),\n      widget: TimePickerWidget,\n      required: true,\n      widgetProps: {\n        beginLimit: startOfDay,\n        endLimit: endOfDay,\n        step: 15,\n        onChange: value => {\n          this.setState({\n            render_helper: !this.state.render_helper\n          });\n        }\n      },\n      colSpan: 2\n    }, {\n      key: 'sbtsk_end_time',\n      label: /*#__PURE__*/React.createElement(\"b\", {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1042,\n          columnNumber: 21\n        }\n      }, /*#__PURE__*/React.createElement(\"i\", {\n        className: \"icon icon-timepicker gx-mr-2\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1043,\n          columnNumber: 25\n        }\n      }), \"End Time\"),\n      widget: TimePickerWidget,\n      required: true,\n      widgetProps: {\n        beginLimit: startTimeFrEndTime ? startTimeFrEndTime : startOfDay,\n        endLimit: endOfDay,\n        step: 15\n      },\n      colSpan: 2\n    }];\n    let configuredColSpan = ((_this$props$updateTyp7 = this.props.updateTypeDetails) === null || _this$props$updateTyp7 === void 0 ? void 0 : (_this$props$updateTyp8 = _this$props$updateTyp7.config_data) === null || _this$props$updateTyp8 === void 0 ? void 0 : _this$props$updateTyp8[`subtask_status_${this.props.updateTypeId}_fields_colspan`]) || 4;\n    const showRemarksAtBottom = (_this$props$updateTyp9 = this.props.updateTypeDetails) === null || _this$props$updateTyp9 === void 0 ? void 0 : (_this$props$updateTyp0 = _this$props$updateTyp9.config_data) === null || _this$props$updateTyp0 === void 0 ? void 0 : _this$props$updateTyp0[`move_remarks_field_to_bottom_for_${this.props.updateTypeId}`];\n    const meta = {\n      columns: 1,\n      formItemLayout: null,\n      fields: [\n      //check\n      ...(isStatusConfiguredFrParts ? [this.getPartsData()] : []), ...this.getSpecificFieldsFrStatus().fields, ...(this.props.updateTypeId == 'sbtsk_can_postpone' ? postponeFields : []), ...(!showRemarksAtBottom ? [this.getRemarkFieldFrMeta(configuredColSpan)] : [])]\n    };\n    if (showOtpField && doSrvcReqHasOtpInFormData && (updateTypeDetails === null || updateTypeDetails === void 0 ? void 0 : updateTypeDetails.value) == seletedStatusForOtpVerification && (selectedSrvcTypeIdForOtpVerification === null || selectedSrvcTypeIdForOtpVerification === void 0 ? void 0 : selectedSrvcTypeIdForOtpVerification.includes(srvc_type_id_of_sbtsk))) {\n      meta.fields.push({\n        key: `otp`,\n        colSpan: 4,\n        label: `Enter ${otp_field_label}`,\n        widget: 'input',\n        rules: [{\n          pattern: new RegExp('^[0-9]{4}$'),\n          message: 'Please enter 4 digit number only!',\n          required: true\n        }]\n      }, {\n        colSpan: 4,\n        render: () => {\n          return /*#__PURE__*/React.createElement(React.Fragment, null, this.state.otp_expiry && this.state.otp_expiry > Math.floor(Date.now()) ? /*#__PURE__*/React.createElement(\"span\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1113,\n              columnNumber: 37\n            }\n          }, \"Retry in\", ' ', /*#__PURE__*/React.createElement(Countdown, {\n            className: \"gx-d-inline-block\",\n            title: null,\n            format: \"ss\",\n            value: this.state.otp_expiry,\n            onFinish: () => {\n              this.onOTPCountDownFinish();\n            },\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1115,\n              columnNumber: 41\n            }\n          })) : /*#__PURE__*/React.createElement(Button, {\n            onClick: () => this.reGenerateOtp(),\n            type: \"primary\",\n            className: \"gx-btn-outline-info m-2\",\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 37\n            }\n          }, /*#__PURE__*/React.createElement(UndoOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1131,\n              columnNumber: 41\n            }\n          }), ' ', `Resend ${otp_field_label}`), this.state.otpRegenrated && /*#__PURE__*/React.createElement(\"p\", {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 37\n            }\n          }, \"OTP has been sent to Customer Mobile no\"));\n        }\n      });\n    }\n    if (showRemarksAtBottom) {\n      meta.fields.push(this.getRemarkFieldFrMeta(configuredColSpan));\n    }\n    return meta;\n  }\n  getPartsData() {\n    return {\n      key: 'sbtsk_parts_consumption',\n      colSpan: 1,\n      render: () => {\n        var _this$state$viewData21, _this$state$viewData22, _this$state$viewData23, _this$state$viewData24, _this$state$viewData25, _this$state$viewData26, _this$state$viewData27;\n        return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(PartsEditor, {\n          itemEditorData: (_this$state$viewData21 = this.state.viewData) === null || _this$state$viewData21 === void 0 ? void 0 : (_this$state$viewData22 = _this$state$viewData21.form_data) === null || _this$state$viewData22 === void 0 ? void 0 : (_this$state$viewData23 = _this$state$viewData22.parts_data) === null || _this$state$viewData23 === void 0 ? void 0 : (_this$state$viewData24 = _this$state$viewData23.data) === null || _this$state$viewData24 === void 0 ? void 0 : _this$state$viewData24.data,\n          onPartsUpdate: this.handlePartsUpdate,\n          showPrice: (_this$state$viewData25 = this.state.viewData) === null || _this$state$viewData25 === void 0 ? void 0 : (_this$state$viewData26 = _this$state$viewData25.sbtsk_config_data[0]) === null || _this$state$viewData26 === void 0 ? void 0 : (_this$state$viewData27 = _this$state$viewData26.config_data) === null || _this$state$viewData27 === void 0 ? void 0 : _this$state$viewData27.show_part_price_to_assignee,\n          formRef: this.formRef,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 1160,\n            columnNumber: 25\n          }\n        }));\n      }\n    };\n  }\n  isDynamicForm() {\n    var _this$props$updateTyp1, _this$props$updateTyp10;\n    return (_this$props$updateTyp1 = this.props.updateTypeDetails) === null || _this$props$updateTyp1 === void 0 ? void 0 : (_this$props$updateTyp10 = _this$props$updateTyp1.config_data) === null || _this$props$updateTyp10 === void 0 ? void 0 : _this$props$updateTyp10[`sbtsk_status_enable_dynamic_form_for_${this.props.updateTypeId}_status`];\n  }\n  getCustomFieldsJsonFrmConfig() {\n    var _this$props$updateTyp11;\n    if (this.state.manipulatedDynamicMeta) {\n      return JSON.stringify({\n        translatedFields: this.state.manipulatedDynamicMeta.filter(singleFieldMeta => singleFieldMeta.hide != true)\n      });\n    }\n    return getCustomFieldsJsonFrStatus(this.props.updateTypeId, (_this$props$updateTyp11 = this.props.updateTypeDetails) === null || _this$props$updateTyp11 === void 0 ? void 0 : _this$props$updateTyp11.config_data);\n  }\n  getSpecificFieldsFrStatus() {\n    let customFields = decodeFieldsMetaFrmJson(this.getCustomFieldsJsonFrmConfig(), undefined, false, true, this.formRef, () => {\n      if (this.isDynamicForm()) {\n        this.onFormValueChanged({}, this.formRef.current.getFieldsValue());\n      } else {\n        console.log('Not a dynamic form');\n      }\n    }, false, this.bleComponentProps(), fieldId => this.onCustomButtonClick(fieldId));\n    // console.log('Custom fields for status',customFields.map(fieldMeta=>{fieldMeta.colSpan=1; return fieldMeta}));\n    const meta = {\n      columns: 4,\n      formItemLayout: null,\n      fields: customFields.length > 0 ? [\n      // {\n      //     key: 'specific_details_fr_status',\n      //     colSpan: 4,\n      //     render() {\n      //     return (\n      //         <fieldset>\n      //         <legend><b>Specific details</b></legend>\n      //         </fieldset>\n      //     )\n      //     },\n      // },\n      ...customFields.map(fieldMeta => {\n        fieldMeta.colSpan = fieldMeta.colSpan || 4;\n        return fieldMeta;\n      })] : []\n    };\n    return meta;\n  }\n  getUpdateTypeDetails() {\n    var _this$props$updateTyp12;\n    let updateTypeId = this.props.updateTypeId;\n    let statuses = (_this$props$updateTyp12 = this.props.updateTypeDetails) === null || _this$props$updateTyp12 === void 0 ? void 0 : _this$props$updateTyp12.statuses;\n    let returnUpdateTypeDetails;\n    if (statuses) statuses.map(singleStatus => {\n      if (singleStatus.value == updateTypeId) {\n        returnUpdateTypeDetails = singleStatus;\n      }\n    });\n    return returnUpdateTypeDetails;\n  }\n  onFilesChanged(section, files, uploadedFiles) {\n    let newFilesBySection = this.state.filesBySection;\n    newFilesBySection[section] = files;\n    //removing state as we are handling this with draft\n    // this.setState({\n    //     filesBySection : newFilesBySection\n    // })\n    let uploadedFilesBySection = this.state.uploadedFilesBySection;\n    uploadedFilesBySection[section] = uploadedFiles;\n    this.setState({\n      uploadedFilesBySection\n    });\n    if (this.state.TMS250617437781) {\n      let attachments = newFilesBySection;\n      DraftManager.updateDraft(this.draftParams(), attachments, 'attachments');\n    }\n  }\n  onMicFilesChanged(section, files) {\n    let newFilesBySection = this.state.micRecordingsBySection;\n    newFilesBySection[section] = files;\n    //removing state as we are handling this with draft\n    // this.setState({\n    //     micRecordingsBySection : newFilesBySection\n    // })\n    if (this.state.TMS250617437781) {\n      let micFiles = newFilesBySection;\n      DraftManager.updateDraft(this.draftParams(), micFiles, 'mic_files');\n    }\n  }\n  onCameraFilesChanged(section, files) {\n    let newFilesBySection = this.state.cameraRecordingsBySection;\n    newFilesBySection[section] = files;\n    //removing state as we are handling this with draft\n    // this.setState({\n    //     cameraRecordingsBySection : newFilesBySection\n    // })\n    if (this.state.TMS250617437781) {\n      let cameraFiles = newFilesBySection;\n      DraftManager.updateDraft(this.draftParams(), cameraFiles, 'camera_files');\n    }\n  }\n  onFileUploaderReadyChange(section, isReady) {\n    let newSectionWiseReady = this.state.sectionWiseUploaderReady;\n    newSectionWiseReady[section] = isReady;\n    this.setState({\n      sectionWiseUploaderReady: newSectionWiseReady\n    });\n  }\n  onMicFileUploaderReadyChange(section, isReady) {\n    let newSectionWiseReady = this.state.sectionWiseMicUploaderReady;\n    newSectionWiseReady[section] = isReady;\n    this.setState({\n      sectionWiseMicUploaderReady: newSectionWiseReady\n    });\n  }\n  onCameraFileUploaderReadyChange(section, isReady) {\n    let newSectionWiseReady = this.state.sectionWiseCameraUploaderReady;\n    newSectionWiseReady[section] = isReady;\n    this.setState({\n      sectionWiseCameraUploaderReady: newSectionWiseReady\n    });\n  }\n  getAllFileUploadersReady() {\n    let {\n      sectionWiseUploaderReady,\n      sectionWiseMicUploaderReady,\n      sectionWiseCameraUploaderReady\n    } = this.state;\n    let notReady = false;\n    Object.keys(sectionWiseUploaderReady).map(section => {\n      if (!sectionWiseUploaderReady[section]) {\n        notReady = true;\n      }\n    });\n    Object.keys(sectionWiseMicUploaderReady).map(section => {\n      if (!sectionWiseMicUploaderReady[section]) {\n        notReady = true;\n      }\n    });\n    Object.keys(sectionWiseCameraUploaderReady).map(section => {\n      if (!sectionWiseCameraUploaderReady[section]) {\n        notReady = true;\n      }\n    });\n    return !notReady;\n  }\n  render() {\n    var _viewData$form_data, _viewData$form_data2, _this$state$viewData28, _this$state$viewData29, _this$state$viewData30, _this$state$viewData31;\n    const {\n      editorItem\n    } = this.props;\n    const {\n      isFormSubmitting,\n      visible,\n      isLoadingViewData,\n      error,\n      viewData,\n      currentStep,\n      srvcDetails,\n      fileSections,\n      micSections,\n      cameraSections,\n      bleSections,\n      isLoadingLocation,\n      locationData,\n      isExecutingDynamicFormLogic,\n      disableSubmissionButton\n    } = this.state;\n    // console.log(\"Task update editor props - \",this.props);\n    const updateTypeDetails = this.getUpdateTypeDetails();\n    const mandatoryStatusReq = (viewData === null || viewData === void 0 ? void 0 : (_viewData$form_data = viewData.form_data) === null || _viewData$form_data === void 0 ? void 0 : _viewData$form_data.mandatory_status) == null ? [] : viewData === null || viewData === void 0 ? void 0 : (_viewData$form_data2 = viewData.form_data) === null || _viewData$form_data2 === void 0 ? void 0 : _viewData$form_data2.mandatory_status;\n    let editorTitle = 'Update task';\n    let editorColor = '#e1e1e1';\n    let isFormFilledByDraft = false;\n    let draftData = null;\n    // For dynamic forms, force apply draft data to get it before dynamic logic executes\n    if (this.state.TMS250617437781) {\n      draftData = this.isDynamicForm() ? DraftManager.applyDraft(this.draftParams(), true) : DraftManager.applyDraft(this.draftParams());\n      if (draftData && Object.keys(draftData).length > 0) {\n        isFormFilledByDraft = true;\n      }\n    }\n    let updatedDraftData = {\n      ...((_this$state$viewData28 = this.state.viewData) === null || _this$state$viewData28 === void 0 ? void 0 : (_this$state$viewData29 = _this$state$viewData28.form_data) === null || _this$state$viewData29 === void 0 ? void 0 : _this$state$viewData29.form_data),\n      ...draftData\n    };\n    updatedDraftData = this.removeUploadedFilesFromDraft(updatedDraftData, this.state.uploadedFilesBySection);\n    let prefillFormData = convertDateFieldsToMoments(updatedDraftData, this.getRequestInfoMeta().fields);\n    const allFileUploadersReady = this.getAllFileUploadersReady();\n    if (updateTypeDetails) {\n      editorTitle = 'Update task as ' + updateTypeDetails.title;\n      editorColor = updateTypeDetails.color;\n    } else if (this.props.updateTypeId == 'sbtsk_can_postpone' || this.props.updateTypeId == 'sbtsk_can_reject') {\n      var _prefillFormData;\n      editorTitle = this.props.updateTypeId == 'sbtsk_can_postpone' ? 'Postpone task' : 'Reject task';\n      editorColor = this.props.updateTypeId == 'sbtsk_can_postpone' ? '#13c2c2' : '#f5222d';\n      let attachments = (_prefillFormData = prefillFormData) === null || _prefillFormData === void 0 ? void 0 : _prefillFormData.attachments;\n      prefillFormData = {};\n      prefillFormData['attachments'] = attachments ? attachments : {};\n    }\n    if (!this.state.TMS250523533568) {\n      setRemarkFieldAsNoRemarkIfEmpty(prefillFormData, this.formRef);\n    }\n    return /*#__PURE__*/React.createElement(Modal, {\n      title: /*#__PURE__*/React.createElement(\"span\", {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1471,\n          columnNumber: 21\n        }\n      }, /*#__PURE__*/React.createElement(\"i\", {\n        style: {\n          color: editorColor\n        },\n        className: `icon icon-circle gx-mr-2 gx-mt-2 gx-vertical-align-middle`,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1472,\n          columnNumber: 25\n        }\n      }), editorTitle),\n      visible: visible,\n      onOk: this.handleOk,\n      confirmLoading: isFormSubmitting,\n      width: 1500,\n      style: {\n        marginTop: '-70px'\n      },\n      bodyStyle: {\n        minHeight: '85vh',\n        padding: '18px',\n        paddingTop: '0px'\n      },\n      footer: null,\n      onCancel: this.handleCancel,\n      afterClose: this.state.TMS250617437781 && DraftManager.applyDraft(this.draftParams(), this.isDynamicForm()) ? this.showDraftSavedPopUp : null,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1469,\n        columnNumber: 13\n      }\n    }, isLoadingViewData ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-loader-view gx-loader-position\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1504,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(CircularProgress, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1505,\n        columnNumber: 25\n      }\n    })) : viewData == undefined ? /*#__PURE__*/React.createElement(\"p\", {\n      className: \"gx-text-red\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1508,\n        columnNumber: 21\n      }\n    }, error) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Row, {\n      className: \"gx-mt-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1511,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(Col, {\n      xs: 24,\n      md: 14,\n      className: \"gx-border-right gx-mt-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1512,\n        columnNumber: 29\n      }\n    }, mandatoryStatusReq.length > 0 ? /*#__PURE__*/React.createElement(\"div\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1518,\n        columnNumber: 37\n      }\n    }, /*#__PURE__*/React.createElement(\"h5\", {\n      className: \"gx-text-danger\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1519,\n        columnNumber: 41\n      }\n    }, \"Please update following statuses first -\", ' ', this.mandatoryStatusRequirement(mandatoryStatusReq))) : /*#__PURE__*/React.createElement(React.Fragment, null), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-no-header-horizontal-top gx-d-block gx-py-2 gx-px-3\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1531,\n        columnNumber: 33\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \" gx-text-grey\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1532,\n        columnNumber: 37\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-mb-2\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1533,\n        columnNumber: 41\n      }\n    }, \"Assigned by - \", editorItem.c_by, \" -\", ' ', /*#__PURE__*/React.createElement(\"span\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1535,\n        columnNumber: 45\n      }\n    }, /*#__PURE__*/React.createElement(TaskTime, {\n      item: editorItem,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1536,\n        columnNumber: 49\n      }\n    }))), editorItem.title), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-ml-2 gx-border-left gx-border-grey \",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1541,\n        columnNumber: 37\n      }\n    }, /*#__PURE__*/React.createElement(Collapse, {\n      ghost: true\n      // expandIconPosition=\"right\"\n      ,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1542,\n        columnNumber: 41\n      }\n    }, /*#__PURE__*/React.createElement(Collapse.Panel, {\n      header: editorItem.srvc_req_details.description,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1546,\n        columnNumber: 45\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1552,\n        columnNumber: 49\n      }\n    }, /*#__PURE__*/React.createElement(TaskSummaryView, {\n      item: editorItem,\n      sbtsk_config_data: ((_this$state$viewData30 = this.state.viewData) === null || _this$state$viewData30 === void 0 ? void 0 : _this$state$viewData30.sbtsk_config_data.length) > 0 ? (_this$state$viewData31 = this.state.viewData) === null || _this$state$viewData31 === void 0 ? void 0 : _this$state$viewData31.sbtsk_config_data[0] : {},\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1553,\n        columnNumber: 53\n      }\n    })))))), isFormFilledByDraft && /*#__PURE__*/React.createElement(Alert, {\n      className: \"gx-mt-4\",\n      message: \"Unsaved changes, submit form to apply changes\",\n      type: \"error\",\n      showIcon: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1571,\n        columnNumber: 37\n      }\n    }), /*#__PURE__*/React.createElement(Form\n    // {...formItemLayout}\n    , {\n      className: \"gx-w-100 gx-mt-3\",\n      layout: \"vertical\",\n      initialValues: prefillFormData,\n      ref: this.formRef,\n      onFinish: data => {\n        if (this.state.TMS250617437781) {\n          DraftManager.updateDraft(this.draftParams());\n        }\n        this.submitForm(data);\n      },\n      disabled: isExecutingDynamicFormLogic,\n      onValuesChange: (changedValues, allValues) => {\n        if (this.isDynamicForm()) {\n          this.onFormValueChanged(changedValues, allValues);\n        } else {\n          if (this.state.TMS250617437781) {\n            DraftManager.updateDraft(this.draftParams());\n          }\n          console.log('Not a dynamic form');\n        }\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1578,\n        columnNumber: 33\n      }\n    }, isExecutingDynamicFormLogic && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spin_progress\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1614,\n        columnNumber: 45\n      }\n    }, /*#__PURE__*/React.createElement(Spin, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1615,\n        columnNumber: 49\n      }\n    }))), /*#__PURE__*/React.createElement(Row, {\n      style: {\n        flexDirection: 'inherit'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1619,\n        columnNumber: 37\n      }\n    }, /*#__PURE__*/React.createElement(Col, {\n      xs: 24,\n      className: \"gx-pl-0\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1624,\n        columnNumber: 41\n      }\n    }, /*#__PURE__*/React.createElement(FormBuilder, {\n      key: \"info\",\n      meta: this.getRequestInfoMeta(),\n      form: this.formRef,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1625,\n        columnNumber: 45\n      }\n    })), /*#__PURE__*/React.createElement(Col, {\n      xs: 24,\n      md: 24,\n      className: \"gx-pl-0\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1631,\n        columnNumber: 41\n      }\n    }, micSections.map((singleMicSection, index) => {\n      var _prefillFormData2, _prefillFormData2$mic;\n      return /*#__PURE__*/React.createElement(Col, {\n        xs: 24,\n        md: 24,\n        className: \"gx-pl-0\",\n        key: singleMicSection.key,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1638,\n          columnNumber: 53\n        }\n      }, singleMicSection.title != '' && /*#__PURE__*/React.createElement(\"h3\", {\n        className: \"gx-mt-3\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1648,\n          columnNumber: 61\n        }\n      }, singleMicSection.title, /*#__PURE__*/React.createElement(\"hr\", {\n        className: \"gx-bg-dark\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1652,\n          columnNumber: 65\n        }\n      })), /*#__PURE__*/React.createElement(MicInputV2, {\n        authToken: http_utils.getAuthToken(),\n        prefixDomain: http_utils.getCDNDomain(),\n        initialFiles: this.state.editMode ? (_prefillFormData2 = prefillFormData) === null || _prefillFormData2 === void 0 ? void 0 : (_prefillFormData2$mic = _prefillFormData2.mic_files) === null || _prefillFormData2$mic === void 0 ? void 0 : _prefillFormData2$mic[singleMicSection.key] : [],\n        onFilesChanged: files => {\n          this.onMicFilesChanged(singleMicSection.key, files);\n        },\n        onReadyStatusChange: isReady => {\n          this.onMicFileUploaderReadyChange(singleMicSection.key, isReady);\n        },\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1662,\n          columnNumber: 57\n        }\n      }));\n    })), /*#__PURE__*/React.createElement(Col, {\n      xs: 24,\n      md: 24,\n      className: \"gx-pl-0\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1696,\n        columnNumber: 41\n      }\n    }, cameraSections.map((singleCameraSection, index) => {\n      var _prefillFormData3, _prefillFormData3$cam;\n      return /*#__PURE__*/React.createElement(Col, {\n        xs: 24,\n        md: 24,\n        className: \"gx-pl-0\",\n        key: singleCameraSection.key,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1706,\n          columnNumber: 53\n        }\n      }, singleCameraSection.title != '' && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"h3\", {\n        className: \"gx-mt-3\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1717,\n          columnNumber: 65\n        }\n      }, singleCameraSection.required && /*#__PURE__*/React.createElement(\"span\", {\n        style: {\n          color: 'red'\n        },\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1719,\n          columnNumber: 73\n        }\n      }, ' ', \"*\", ' '), singleCameraSection.title, /*#__PURE__*/React.createElement(\"hr\", {\n        className: \"gx-bg-dark\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1731,\n          columnNumber: 69\n        }\n      }))), /*#__PURE__*/React.createElement(CameraInput, {\n        ref: this.cameraRef,\n        authToken: http_utils.getAuthToken(),\n        prefixDomain: http_utils.getCDNDomain(),\n        required: singleCameraSection.required,\n        initialFiles: this.state.editMode ? (_prefillFormData3 = prefillFormData) === null || _prefillFormData3 === void 0 ? void 0 : (_prefillFormData3$cam = _prefillFormData3.camera_files) === null || _prefillFormData3$cam === void 0 ? void 0 : _prefillFormData3$cam[singleCameraSection.key] : [],\n        onFilesChanged: files => {\n          this.onCameraFilesChanged(singleCameraSection.key, files);\n        },\n        onReadyStatusChange: isReady => {\n          this.onCameraFileUploaderReadyChange(singleCameraSection.key, isReady);\n        },\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1742,\n          columnNumber: 57\n        }\n      }));\n    })), /*#__PURE__*/React.createElement(Col, {\n      xs: 24,\n      md: 24,\n      className: \"gx-pl-0\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1780,\n        columnNumber: 41\n      }\n    }, fileSections.map((singleFileSection, index) => {\n      var _prefillFormData4, _prefillFormData4$att;\n      return /*#__PURE__*/React.createElement(Col, {\n        xs: 24,\n        md: 24,\n        className: \"gx-pl-0\",\n        key: singleFileSection.key,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1787,\n          columnNumber: 53\n        }\n      }, singleFileSection.title != '' && /*#__PURE__*/React.createElement(\"h3\", {\n        className: \"gx-mt-3\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1798,\n          columnNumber: 61\n        }\n      }, singleFileSection.required && /*#__PURE__*/React.createElement(\"span\", {\n        style: {\n          color: 'red'\n        },\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1800,\n          columnNumber: 69\n        }\n      }, ' ', \"*\", ' '), singleFileSection.title, /*#__PURE__*/React.createElement(\"hr\", {\n        className: \"gx-bg-dark\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1812,\n          columnNumber: 65\n        }\n      })), /*#__PURE__*/React.createElement(S3Uploader\n      // className=\"gx-w-50\"\n      // demoMode\n      , {\n        required: singleFileSection.required,\n        maxColSpan: 4,\n        authToken: http_utils.getAuthToken(),\n        prefixDomain: http_utils.getCDNDomain(),\n        onFilesChanged: (files, deletedFileUrl, uploadedFiles) => {\n          this.onFilesChanged(singleFileSection.key, files, uploadedFiles);\n        },\n        onReadyStatusChanged: isReady => {\n          this.onFileUploaderReadyChange(singleFileSection.key, isReady);\n        },\n        initialFiles: this.state.editMode ? (_prefillFormData4 = prefillFormData) === null || _prefillFormData4 === void 0 ? void 0 : (_prefillFormData4$att = _prefillFormData4.attachments) === null || _prefillFormData4$att === void 0 ? void 0 : _prefillFormData4$att[singleFileSection.key] : [],\n        isDraftEnabled: true,\n        customPreviewHeight: \"100%\",\n        customFileIconMaxWidth: \"40px\",\n        compConfig: {\n          name: 'task-update-editor-attachments'\n        },\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1822,\n          columnNumber: 57\n        }\n      }));\n    }))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-mt-4\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1872,\n        columnNumber: 37\n      }\n    }, this.needToCaptureLocation() && /*#__PURE__*/React.createElement(LocationDetails, {\n      isLoadingLocation: isLoadingLocation,\n      locationData: locationData,\n      onRetry: () => {\n        this.initGeoFenceStuff();\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1874,\n        columnNumber: 45\n      }\n    })), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-mt-4\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1885,\n        columnNumber: 37\n      }\n    }, !allFileUploadersReady && /*#__PURE__*/React.createElement(Spin, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1887,\n        columnNumber: 45\n      }\n    }), /*#__PURE__*/React.createElement(Button, {\n      type: \"primary\",\n      htmlType: \"submit\",\n      \"data-testid\": \"submit-button\",\n      disabled: isFormSubmitting || !allFileUploadersReady || mandatoryStatusReq.length > 0 || disableSubmissionButton || isExecutingDynamicFormLogic,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1889,\n        columnNumber: 41\n      }\n    }, editorTitle), isFormSubmitting ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"gx-loader-view gx-loader-position\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1905,\n        columnNumber: 45\n      }\n    }, /*#__PURE__*/React.createElement(CircularProgress, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1906,\n        columnNumber: 49\n      }\n    })) : null, error ? /*#__PURE__*/React.createElement(\"p\", {\n      className: \"gx-text-red\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1910,\n        columnNumber: 45\n      }\n    }, error) : null))))));\n  }\n}\nexport default TaskUpdateEditor;", "map": {"version": 3, "names": ["React", "Component", "Modal", "Form", "Radio", "Input", "<PERSON><PERSON>", "Popover", "Steps", "message", "Row", "Col", "Avatar", "<PERSON><PERSON><PERSON>", "Checkbox", "Typography", "Upload", "Timeline", "List", "Collapse", "Spin", "<PERSON><PERSON>", "FormBuilder", "http_utils", "CircularProgress", "AntDesignOutlined", "EllipsisOutlined", "InboxOutlined", "RightOutlined", "ShareAltOutlined", "UndoOutlined", "UploadOutlined", "UserOutlined", "<PERSON><PERSON>", "RemoteSourceSelect", "convertDateFieldsToMoments", "convertUTCToDisplayTime", "durationAsString", "isTimePassed", "priorities", "getGeneralFileSection", "getTouchedFieldsValueInForm", "hasAnyFileChanged", "isAndroidApp", "generateUUID", "setRemarkFieldAsNoRemarkIfEmpty", "showCreateOrUpdateSuccessMessage", "TaskSummaryView", "TaskTime", "TimePickerWidget", "moment", "S3Uploader", "decodeFieldsMetaFrmJson", "decodeFileSectionsFrmJson", "decodeMicSectionsFrmJson", "decodeCameraSectionsFrmJson", "decodeBLESectionsFrmJson", "getCustomFieldsJsonFrStatus", "isArray", "LocationDetails", "defaultLocationVerificationStatus", "MicInputV2", "CameraInput", "Countdown", "SubtaskCardConfigHelper", "_", "DraftManager", "MyTaskConfigHelper", "PartsEditor", "checkFeatureAccess", "protoUrl", "submitUrl", "reSendOtpUrl", "dynamicFormLogicPath", "debouncer", "disabledDate", "current", "subtract", "endOf", "TaskUpdateEditor", "constructor", "props", "initState", "render_helper", "visible", "isFormSubmitting", "viewData", "undefined", "isLoadingViewData", "editMode", "error", "currentStep", "fileSections", "filesBySection", "sectionWiseUploaderReady", "micSections", "micRecordingsBySection", "sectionWiseMicUploaderReady", "cameraSections", "cameraRecordingsBySection", "sectionWiseCameraUploaderReady", "isLoadingLocation", "locationData", "otpRegenrated", "manipulatedDynamicMeta", "isExecutingDynamicFormLogic", "disableSubmissionButton", "updateClosureToChildComponent", "isDynamicFormFirstExec", "uploadedFilesBySection", "partsData", "draftRetryCount", "state", "verifyFeatureAccess", "hasAccess", "setState", "TMS250523533568", "console", "hasDraftAccess", "TMS250617437781", "handleOk", "updateClosureToParent", "handleCancel", "_this$cameraRef", "_this$cameraRef$curre", "resetReadyStatusOfCamera", "cameraRef", "stopCapturing", "hasAnyBarcodeValueChanged", "form_data", "fieldMeta", "prefill_data", "returnFields", "for<PERSON>ach", "singleMeta", "key", "type", "executeDynamicFormLogic", "changedValues", "allValues", "isRetryForDraft", "_this$state", "_this$state$viewData", "_this$state$viewData$", "_this$state2", "_this$state2$viewData", "_this$state2$viewData2", "setIsExecutingDynamicFormLogic", "renderingFrmDraft", "draftData", "applyDraft", "draftParams", "Object", "keys", "length", "params", "meta", "getOriginalMetaFrDynamicForm", "currentMeta", "editor<PERSON><PERSON>", "sbtsk_type", "value", "srvc_req_id", "srvc_type_id", "performPostCall", "id", "updateTypeId", "resp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableFormSubmissionButton", "errorMessage", "data", "config", "duration", "initConfigData", "formRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "applyDraftAndRetryIfNeeded", "updateDraft", "log", "decodeErrorToMessage", "onFormValueChanged", "clearTimeout", "setTimeout", "onCustomButtonClick", "fieldId", "isDynamicForm", "_this$formRef", "_this$formRef$current", "getFieldsValue", "submitForm", "_this$formRef2", "_this$state$viewData2", "_this$state$viewData3", "_this$state3", "_this$state3$viewData", "_this$state3$viewData2", "_this$state4", "_this$state4$viewData", "_this$state4$viewData2", "touchedFields", "getSpecificFieldsFrStatus", "fields", "_this$state$viewData4", "_this$state$viewData5", "_this$state$viewData6", "_this$state$viewData7", "_this$state$viewData8", "_this$state$viewData9", "_this$state$viewData0", "_this$state$viewData1", "_this$state$viewData10", "isAnyFileChanged", "attachments", "isAnyMicFileChanged", "mic_files", "isAnyCameraFileChanged", "camera_files", "merge", "_this$state$viewData11", "canTriggerUpdateWithoutChanges", "statusesToAllowFrMultipleTrigger", "sbtsk_config_data", "info", "needToCaptureLocation", "_this$state$locationD", "_this$state$locationD2", "address", "text", "onComplete", "clearDraft", "tellParentToRefreshList", "entry_id", "onError", "performPutCall", "handlePartsUpdate", "updatedItems", "_this$props", "_this$props$editorIte", "_this$props2", "_this$formRef3", "_this$formRef3$curren", "sbtsk_id", "update_type_id", "field<PERSON><PERSON><PERSON>", "removeUploadedFilesFromDraft", "draft", "uploadedFiles", "newDraft", "JSON", "parse", "stringify", "hasOwnProperty", "filter", "file", "_uploadedFiles$key", "includes", "createRef", "componentDidMount", "initViewData", "initGeoFenceStuff", "getGPSLocationFrmDevice", "_this$props$updateTyp", "_this$props$updateTyp2", "update_status_key", "geo_verification_enabled_statuses", "updateTypeDetails", "config_data", "enable_geo_verification_for", "Promise", "resolve", "reject", "_window$wifyApp", "window", "wifyApp", "getGPSLocation", "_window$wifyApp2", "locationRequestEventName", "addEventListener", "e", "detail", "recivedLocationData", "appCallResp", "success", "_this$props$editorIte2", "srvc_req_details", "url", "performGetCall", "entry_specific_data", "then", "dont_override_files", "_this$props$updateTyp3", "_entry_specific_data$", "_entry_specific_data$3", "_entry_specific_data$5", "isGeneralFileSectionMandatory", "isDefaultAttachmentFieldMandatory", "newFileSections", "custom_file_sections", "getCustomFileSectionsFrmConfig", "_this$props$updateTyp4", "_this$props$updateTyp5", "showAttachMentsAtBottom", "initialFilesBySection", "_entry_specific_data$2", "newMicSections", "customMicSections", "getCustomMicSectionsFrmConfig", "initialMicRecordingsBySection", "_entry_specific_data$4", "newCameraSections", "customCameraSections", "getCustomCameraSectionsFrmConfig", "initialCameraRecordingsBySection", "_entry_specific_data$6", "getCustomFieldsJsonFrmConfig", "componentDidUpdate", "prevProps", "prevState", "showEditor", "refreshOnUpdate", "onClose", "singleKey", "onDataModified", "showDraftSavedPopUp", "style", "marginTop", "content", "mandatoryStatusRequirement", "reqStatusArray", "statusToBeCompleted", "statuses", "singleStatus", "push", "title", "join", "_this$props$updateTyp6", "cust<PERSON><PERSON><PERSON><PERSON>", "_JSON$parse", "<PERSON><PERSON>ields", "getMissingDraftFields", "currentFormValues", "missingFields", "draftValue", "currentValue", "finalizeDraftApplication", "MAX_RETRY_ATTEMPTS", "updatedFormValues", "stillMissingFields", "createParamsFrBleExec", "_this$props$editorIte3", "_this$props$editorIte4", "_this$state5", "_this$state5$viewData", "_this$state5$viewData2", "_this$state6", "_this$state6$viewData", "_this$state6$viewData2", "_this$props$editorIte5", "_this$props3", "_this$state7", "_this$state7$viewData", "_this$state7$viewData2", "sbtsk_type_id", "collab_order_id", "bleComponentProps", "taskUpdateVisible", "paramsFrLambdaExec", "reGenerateOtp", "_this$state8", "_this$state8$viewData", "_this$state8$viewData2", "otp_expiry", "Math", "floor", "Date", "now", "onOTPCountDownFinish", "getRemarkFieldFrMeta", "configuredColSpan", "colSpan", "label", "widget", "rules", "required", "getRequestInfoMeta", "_this$formRef$current2", "_this$state$viewData12", "_this$state$viewData13", "_this$state$viewData14", "_this$state$viewData15", "_this$state$viewData16", "_this$state$viewData17", "_this$state9", "_this$state9$viewData", "_this$state9$viewData2", "_this$state0", "_this$state0$viewData", "_this$state0$viewData2", "_this$state1", "_this$state1$viewData", "_this$state1$viewData2", "_this$state10", "_this$state10$viewDat", "_this$state10$viewDat2", "_this$state11", "_this$state11$viewDat", "_this$state11$viewDat2", "_this$props4", "_this$props4$editorIt", "_this$state$viewData18", "_this$state$viewData19", "_this$state$viewData20", "_this$props$updateTyp7", "_this$props$updateTyp8", "_this$props$updateTyp9", "_this$props$updateTyp0", "startTimeFrEndTime", "getFieldValue", "startOfDay", "sbtsk_time_slot_lower_limit", "endOfDay", "sbtsk_time_slot_upper_limit", "showOtpField", "configure_consumer_otp_verification", "doSrvcReqHasOtpInFormData", "srvc_req_has_otp", "seletedStatusForOtpVerification", "selected_status_for_otp_verification", "selectedSrvcTypeIdForOtpVerification", "selected_srvc_type_id_for_otp_verification", "srvc_type_id_of_sbtsk", "otp_field_label", "getUpdateTypeDetails", "statusConfiguredFrParts", "statues_for_parts_consumption", "isStatusConfiguredFrParts", "postponeFields", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "widgetProps", "width", "onChange", "dateString", "sbtsk_start_day", "utc", "render", "Fragment", "beginLimit", "endLimit", "step", "showRemarksAtBottom", "columns", "formItemLayout", "getPartsData", "pattern", "RegExp", "format", "onFinish", "onClick", "_this$state$viewData21", "_this$state$viewData22", "_this$state$viewData23", "_this$state$viewData24", "_this$state$viewData25", "_this$state$viewData26", "_this$state$viewData27", "itemEditorData", "parts_data", "onPartsUpdate", "showPrice", "show_part_price_to_assignee", "_this$props$updateTyp1", "_this$props$updateTyp10", "_this$props$updateTyp11", "singleFieldMeta", "hide", "customFields", "map", "_this$props$updateTyp12", "returnUpdateTypeDetails", "onFilesChanged", "section", "files", "newFilesBySection", "onMicFilesChanged", "mic<PERSON>iles", "onCameraFilesChanged", "cameraFiles", "onFileUploaderReadyChange", "isReady", "newSectionWiseReady", "onMicFileUploaderReadyChange", "onCameraFileUploaderReadyChange", "getAllFileUploadersReady", "notReady", "_viewData$form_data", "_viewData$form_data2", "_this$state$viewData28", "_this$state$viewData29", "_this$state$viewData30", "_this$state$viewData31", "srvcDetails", "bleSections", "mandatoryStatusReq", "mandatory_status", "editor<PERSON><PERSON><PERSON>", "editorColor", "isFormFilledByDraft", "updatedDraftData", "prefillFormData", "allFileUploadersReady", "color", "_prefillFormData", "onOk", "confirmLoading", "bodyStyle", "minHeight", "padding", "paddingTop", "footer", "onCancel", "afterClose", "xs", "md", "c_by", "item", "ghost", "Panel", "header", "description", "showIcon", "layout", "initialValues", "ref", "disabled", "onValuesChange", "flexDirection", "form", "singleMicSection", "index", "_prefillFormData2", "_prefillFormData2$mic", "authToken", "getAuthToken", "prefixDomain", "getCDNDomain", "initialFiles", "onReadyStatusChange", "singleCameraSection", "_prefillFormData3", "_prefillFormData3$cam", "singleFileSection", "_prefillFormData4", "_prefillFormData4$att", "maxColSpan", "deletedFileUrl", "onReadyStatusChanged", "isDraftEnabled", "customPreviewHeight", "customFileIconMaxWidth", "compConfig", "name", "onRetry", "htmlType"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/routes/my-tasks/TaskUpdateEditor.js"], "sourcesContent": ["import React, { Component } from 'react';\r\nimport {\r\n    Modal,\r\n    Form,\r\n    Radio,\r\n    Input,\r\n    Button,\r\n    Popover,\r\n    Steps,\r\n    message,\r\n    Row,\r\n    Col,\r\n    Avatar,\r\n    Tooltip,\r\n    Checkbox,\r\n    Typography,\r\n    Upload,\r\n    Timeline,\r\n    List,\r\n    Collapse,\r\n    Spin,\r\n    Alert,\r\n} from 'antd';\r\nimport FormBuilder from 'antd-form-builder';\r\nimport http_utils from '../../util/http_utils';\r\nimport CircularProgress from '../../components/CircularProgress';\r\nimport {\r\n    AntDesignOutlined,\r\n    EllipsisOutlined,\r\n    InboxOutlined,\r\n    RightOutlined,\r\n    ShareAltOutlined,\r\n    UndoOutlined,\r\n    UploadOutlined,\r\n    UserOutlined,\r\n} from '@ant-design/icons';\r\n// import ReactTags from 'react-tag-autocomplete'\r\nimport <PERSON>agger from 'antd/lib/upload/Dragger';\r\nimport RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';\r\nimport {\r\n    convertDateFieldsToMoments,\r\n    convertUTCToDisplayTime,\r\n    durationAsString,\r\n    isTimePassed,\r\n    priorities,\r\n    getGeneralFileSection,\r\n    getTouchedFieldsValueInForm,\r\n    hasAnyFileChanged,\r\n    isAndroidApp,\r\n    generateUUID,\r\n    setRemarkFieldAsNoRemarkIfEmpty,\r\n    showCreateOrUpdateSuccessMessage,\r\n} from '../../util/helpers';\r\nimport TaskSummaryView from '../../components/WIFY/subtasks/TaskSummaryView';\r\nimport TaskTime from '../../components/WIFY/subtasks/TaskTime';\r\nimport TimePickerWidget from '../../components/wify-utils/TimePickerWidget';\r\nimport moment from 'moment';\r\nimport S3Uploader from '../../components/wify-utils/S3Uploader/S3Uploader';\r\nimport {\r\n    decodeFieldsMetaFrmJson,\r\n    decodeFileSectionsFrmJson,\r\n    decodeMicSectionsFrmJson,\r\n    decodeCameraSectionsFrmJson,\r\n    decodeBLESectionsFrmJson,\r\n} from '../../components/wify-utils/FieldCreator/helpers';\r\nimport { getCustomFieldsJsonFrStatus } from './SingleStatusUpdates';\r\nimport { isArray } from 'lodash';\r\nimport LocationDetails from './LocationDetails';\r\nimport { defaultLocationVerificationStatus } from '../../components/WIFY/helpers';\r\nimport MicInputV2 from '../../components/wify-utils/MicInput_v2';\r\nimport CameraInput from '../../components/wify-utils/CameraInput';\r\nimport Countdown from 'antd/lib/statistic/Countdown';\r\nimport SubtaskCardConfigHelper from '../../components/WIFY/subtasks/SubtaskCardConfigHelper';\r\nimport _ from 'lodash';\r\nimport { DraftManager } from './DraftManager';\r\nimport MyTaskConfigHelper from './MyTaskConfigHelper';\r\nimport PartsEditor from './PartsEditor';\r\nimport checkFeatureAccess from '../../util/FeatureAccess';\r\n\r\n// import TimelineCard from './TimelineCard';\r\n// import {Paragraph} from 'antd';\r\n\r\nconst protoUrl = '/my-tasks/proto';\r\nconst submitUrl = '/my-tasks';\r\n// const otpVerityUrl = \"/my-tasks/otp-verify\";\r\nconst reSendOtpUrl = '/my-tasks/re-send-otp';\r\nconst dynamicFormLogicPath = '/my-tasks/exec-dynamic-form-logic';\r\nlet debouncer;\r\n\r\nfunction disabledDate(current) {\r\n    // Can not select days before today and today\r\n    return current && current < moment().subtract(1, 'days').endOf('day');\r\n}\r\n\r\nclass TaskUpdateEditor extends Component {\r\n    constructor(props) {\r\n        super(props);\r\n        this.formRef = React.createRef();\r\n        this.cameraRef = React.createRef();\r\n        // console.log('Rxd props task update editor',props);\r\n    }\r\n    initState = {\r\n        render_helper: false,\r\n        visible: false,\r\n        isFormSubmitting: false,\r\n        viewData: undefined,\r\n        isLoadingViewData: false,\r\n        editMode: this.props.editMode,\r\n        error: '',\r\n        currentStep: 0,\r\n        // Attachements and custom files section\r\n        fileSections: [],\r\n        filesBySection: {},\r\n        sectionWiseUploaderReady: {},\r\n        // Mic sections\r\n        micSections: [],\r\n        micRecordingsBySection: {},\r\n        sectionWiseMicUploaderReady: {},\r\n        //Camera section\r\n        cameraSections: [],\r\n        cameraRecordingsBySection: {},\r\n        sectionWiseCameraUploaderReady: {},\r\n        // ---\r\n        isLoadingLocation: false,\r\n        locationData: undefined,\r\n        //otp section\r\n        otpRegenrated: false,\r\n        // dynamic form\r\n        manipulatedDynamicMeta: undefined,\r\n        isExecutingDynamicFormLogic: false,\r\n        disableSubmissionButton: false,\r\n        updateClosureToChildComponent: true,\r\n        isDynamicFormFirstExec: true,\r\n        uploadedFilesBySection: {},\r\n        partsData: [],\r\n        draftRetryCount: 0, // Track retry attempts for draft application\r\n    };\r\n\r\n    state = this.initState;\r\n\r\n    componentDidMount() {\r\n        this.initViewData();\r\n        this.verifyFeatureAccess();\r\n    }\r\n\r\n    verifyFeatureAccess = async () => {\r\n        try {\r\n            let hasAccess = await checkFeatureAccess('TMS250523533568');\r\n            this.setState({ TMS250523533568: hasAccess });\r\n        } catch (error) {\r\n            this.setState({ TMS250523533568: false });\r\n            console.error(\r\n                'TaskUpdateEditor :: verifyFeatureAccess :: error : ',\r\n                error\r\n            );\r\n        }\r\n\r\n        // Check access for draft functionality feature flag\r\n        try {\r\n            let hasDraftAccess = await checkFeatureAccess('TMS250617437781');\r\n            this.setState({ TMS250617437781: hasDraftAccess });\r\n        } catch (error) {\r\n            this.setState({ TMS250617437781: false });\r\n            console.error(\r\n                'TaskUpdateEditor :: verifyFeatureAccess :: TMS250617437781 :: error : ',\r\n                error\r\n            );\r\n        }\r\n    };\r\n\r\n    initGeoFenceStuff() {\r\n        // console.log('CDM');\r\n        // console.log('props',this.props);\r\n        // console.log('config_data',this.props.updateTypeDetails?.config_data);\r\n\r\n        // message.success(\r\n        //     isAndroidApp() ?\r\n        //     'Is android app' :\r\n        //     'Not android app'\r\n        // )\r\n\r\n        // console.log('isArray ',isArray(geo_verification_enabled_statuses))\r\n        // console.log('update_status_key',update_status_key);\r\n        if (this.needToCaptureLocation()) {\r\n            this.setState({\r\n                isLoadingLocation: true,\r\n            });\r\n            this.getGPSLocationFrmDevice();\r\n            // console.log('Check if Geo verification is enabled for the status & whether this is android App passed');\r\n        }\r\n    }\r\n\r\n    needToCaptureLocation() {\r\n        const update_status_key = this.props.updateTypeId;\r\n        const geo_verification_enabled_statuses =\r\n            this.props.updateTypeDetails?.config_data\r\n                ?.enable_geo_verification_for;\r\n        return (\r\n            isArray(geo_verification_enabled_statuses) &&\r\n            geo_verification_enabled_statuses.includes(update_status_key)\r\n        );\r\n        // TODO uncomment before push && isAndroidApp()\r\n    }\r\n\r\n    getGPSLocationFrmDevice() {\r\n        return new Promise((resolve, reject) => {\r\n            console.log('gpsLocation', 'Getting GPS location..');\r\n            if (window.wifyApp?.getGPSLocation) {\r\n                const locationRequestEventName =\r\n                    'location_request_' + generateUUID();\r\n                window.addEventListener(locationRequestEventName, (e) => {\r\n                    const locationData = e.detail;\r\n                    setTimeout(() => {\r\n                        this.recivedLocationData(locationData);\r\n                    }, 1000);\r\n\r\n                    // console.log('Received gpsLocation from android -',JSON.stringify(e.detail));\r\n                });\r\n                const appCallResp = window.wifyApp?.getGPSLocation(\r\n                    locationRequestEventName\r\n                );\r\n                resolve();\r\n            } else {\r\n                // const dummyLocationData = {\"latitude\":19.2181636,\"longitude\":72.8596517,\"isMock\":false,\"timestamp\":1653559234,\"speed\":0,\"bearing\":0,\"altitude\":-47,\"accuracy\":49.86800003051758,\"address\":{\"text\":\"RAJNANDHAN CHS A-D, SAMVED CO-OPERATIVE HOUSING SOCIETY SAMVEL CHS, राजेंद्र नगर, बोरिवली ईस्ट, मुंबई, महाराष्ट्र 400066, India\",\"area\":\"महाराष्ट्र\",\"locality\":\"मुंबई\",\"postalCode\":\"400066\"},\"device_info\":{\"device_unique_id\":\"0e7ca81e10524a85\",\"device_android_version\":\"Android v10.0, API Level: 29\",\"device_mode_name\":\"HMD Global Nokia 6.1\",\"battery\":96,\"battery_acc\":false}}\r\n                // console.log('Loaded dummy location data');\r\n                message.success('Please update your app');\r\n                // setTimeout(()=>{\r\n                //     this.recivedLocationData(dummyLocationData);\r\n                // },1000)\r\n                resolve();\r\n            }\r\n        });\r\n    }\r\n\r\n    recivedLocationData(locationData) {\r\n        this.setState({\r\n            isLoadingLocation: false,\r\n            locationData: locationData,\r\n        });\r\n    }\r\n\r\n    initViewData() {\r\n        // console.log(\"Trying to init view Data\",this.state.visible);\r\n        this.initGeoFenceStuff();\r\n        if (\r\n            (this.state.editMode && this.state.visible) ||\r\n            (!this.state.editMode &&\r\n                this.state.viewData == undefined &&\r\n                !this.state.isLoadingViewData)\r\n        ) {\r\n            this.setState({\r\n                isLoadingViewData: true,\r\n            });\r\n            var params = {};\r\n            params['sbtsk_type_id'] =\r\n                '' + this.props.editorItem.sbtsk_type.value;\r\n            params['srvcReqId'] = this.props.editorItem.srvc_req_details?.id;\r\n            const onComplete = (resp) => {\r\n                this.initConfigData(\r\n                    resp,\r\n                    // then set loading false\r\n                    this.setState(\r\n                        {\r\n                            isLoadingViewData: false,\r\n                            viewData: resp.data,\r\n                            error: '',\r\n                        },\r\n                        () => {\r\n                            if (this.isDynamicForm()) {\r\n                                this.onFormValueChanged({}, {}); // CDM\r\n                            }\r\n                        }\r\n                    )\r\n                );\r\n            };\r\n            const onError = (error) => {\r\n                // console.log(error.response.status);\r\n                this.setState({\r\n                    isLoadingViewData: false,\r\n                    error: http_utils.decodeErrorToMessage(error),\r\n                });\r\n            };\r\n            var url =\r\n                protoUrl +\r\n                '/' +\r\n                this.props.editorItem.id +\r\n                '/' +\r\n                this.props.updateTypeId;\r\n            // console.log(url);\r\n            http_utils.performGetCall(url, params, onComplete, onError);\r\n        }\r\n    }\r\n\r\n    initConfigData(entry_specific_data, then, dont_override_files = false) {\r\n        let isGeneralFileSectionMandatory =\r\n            SubtaskCardConfigHelper.isDefaultAttachmentFieldMandatory(\r\n                this.props.updateTypeDetails?.config_data,\r\n                this.props.updateTypeId\r\n            );\r\n        let newFileSections = [\r\n            getGeneralFileSection(isGeneralFileSectionMandatory),\r\n        ];\r\n        let custom_file_sections = this.getCustomFileSectionsFrmConfig();\r\n        if (custom_file_sections && custom_file_sections.length > 0) {\r\n            const showAttachMentsAtBottom =\r\n                this.props.updateTypeDetails?.config_data?.[\r\n                    `move_attachments_field_to_bottom_for_${this.props.updateTypeId}`\r\n                ];\r\n            newFileSections = showAttachMentsAtBottom\r\n                ? [...custom_file_sections, ...newFileSections]\r\n                : [...newFileSections, ...custom_file_sections];\r\n        }\r\n        // console.log('newFileSections',newFileSections)\r\n\r\n        // Prefilling attachments\r\n        let initialFilesBySection = dont_override_files\r\n            ? this.state.filesBySection\r\n            : {};\r\n\r\n        if (entry_specific_data.data.form_data?.form_data) {\r\n            initialFilesBySection =\r\n                entry_specific_data.data.form_data?.form_data['attachments'];\r\n        }\r\n\r\n        let newMicSections = [];\r\n        let customMicSections = this.getCustomMicSectionsFrmConfig();\r\n        if (customMicSections && customMicSections.length > 0) {\r\n            newMicSections = [...newMicSections, ...customMicSections];\r\n        }\r\n\r\n        let initialMicRecordingsBySection = dont_override_files\r\n            ? this.state.micRecordingsBySection\r\n            : {};\r\n\r\n        if (entry_specific_data.data.form_data?.form_data) {\r\n            initialMicRecordingsBySection =\r\n                entry_specific_data.data.form_data?.form_data['mic_files'];\r\n        }\r\n\r\n        let newCameraSections = [];\r\n        let customCameraSections = this.getCustomCameraSectionsFrmConfig();\r\n        if (customCameraSections && customCameraSections.length > 0) {\r\n            newCameraSections = [...newCameraSections, ...customCameraSections];\r\n        }\r\n\r\n        let initialCameraRecordingsBySection = dont_override_files\r\n            ? this.state.cameraRecordingsBySection\r\n            : {};\r\n        if (entry_specific_data.data.form_data?.form_data) {\r\n            initialCameraRecordingsBySection =\r\n                entry_specific_data.data.form_data?.form_data['camera_files'];\r\n        }\r\n\r\n        this.setState(\r\n            {\r\n                fileSections: newFileSections,\r\n                filesBySection: { ...initialFilesBySection },\r\n                micSections: newMicSections,\r\n                micRecordingsBySection: { ...initialMicRecordingsBySection },\r\n                cameraSections: newCameraSections,\r\n                cameraRecordingsBySection: {\r\n                    ...initialCameraRecordingsBySection,\r\n                },\r\n            },\r\n            then\r\n        );\r\n    }\r\n\r\n    getCustomMicSectionsFrmConfig() {\r\n        return decodeMicSectionsFrmJson(this.getCustomFieldsJsonFrmConfig());\r\n    }\r\n\r\n    getCustomCameraSectionsFrmConfig() {\r\n        return decodeCameraSectionsFrmJson(this.getCustomFieldsJsonFrmConfig());\r\n    }\r\n\r\n    getCustomFileSectionsFrmConfig() {\r\n        return decodeFileSectionsFrmJson(this.getCustomFieldsJsonFrmConfig());\r\n    }\r\n\r\n    componentDidUpdate(prevProps, prevState) {\r\n        if (\r\n            prevProps.editorItem != this.props.editorItem ||\r\n            prevProps.showEditor != this.props.showEditor\r\n        ) {\r\n            this.setState(\r\n                {\r\n                    render_helper: !this.state.render_helper,\r\n                    visible: this.props.showEditor,\r\n                    draftRetryCount: 0, // Reset retry counter when editor opens\r\n                },\r\n                function () {\r\n                    if (this.props.showEditor && this.state.editMode) {\r\n                        this.initViewData();\r\n                    }\r\n                }\r\n            );\r\n        } else {\r\n            if (this.state.refreshOnUpdate) {\r\n                this.setState(\r\n                    {\r\n                        refreshOnUpdate: false,\r\n                    },\r\n                    this.initViewData()\r\n                );\r\n            }\r\n        }\r\n    }\r\n\r\n    handleOk = () => {\r\n        this.setState({\r\n            visible: false,\r\n            isFormSubmitting: false,\r\n        });\r\n        this.updateClosureToParent();\r\n    };\r\n\r\n    updateClosureToParent() {\r\n        if (this.props.onClose != undefined) {\r\n            this.props.onClose();\r\n        }\r\n        this.setState({\r\n            refreshOnUpdate: true,\r\n            ...this.initState,\r\n        });\r\n    }\r\n    resetReadyStatusOfCamera() {\r\n        Object.keys(this.state.sectionWiseCameraUploaderReady).forEach(\r\n            (singleKey) =>\r\n                (this.state.sectionWiseCameraUploaderReady[singleKey] = true)\r\n        );\r\n    }\r\n\r\n    tellParentToRefreshList(entry_id) {\r\n        // console.log(\"Trying to to tell parent to refresh list\");\r\n        if (this.props.onDataModified != undefined) {\r\n            this.props.onDataModified(entry_id);\r\n        }\r\n    }\r\n\r\n    showDraftSavedPopUp() {\r\n        message.info(\r\n            {\r\n                style: {\r\n                    marginTop: '50vh',\r\n                },\r\n                content: 'Saved as draft',\r\n            },\r\n            3000\r\n        );\r\n    }\r\n\r\n    handleCancel = () => {\r\n        this.setState(\r\n            {\r\n                updateClosureToChildComponent: false,\r\n            },\r\n            () => {\r\n                this.setState({\r\n                    visible: false,\r\n                });\r\n                this.resetReadyStatusOfCamera();\r\n                if (this.cameraRef?.current?.stopCapturing) {\r\n                    this.cameraRef.current.stopCapturing();\r\n                }\r\n                this.updateClosureToParent();\r\n                this.setState({\r\n                    uploadedFilesBySection: {},\r\n                });\r\n            }\r\n        );\r\n    };\r\n\r\n    mandatoryStatusRequirement(reqStatusArray) {\r\n        let statusToBeCompleted = [];\r\n        this.props.updateTypeDetails.statuses.forEach((singleStatus) => {\r\n            if (reqStatusArray.includes(singleStatus.value)) {\r\n                statusToBeCompleted.push(singleStatus.title);\r\n            }\r\n        });\r\n        return statusToBeCompleted.join(', ');\r\n    }\r\n\r\n    hasAnyBarcodeValueChanged = (form_data, fieldMeta, prefill_data) => {\r\n        let returnFields = {};\r\n\r\n        fieldMeta.forEach((singleMeta) => {\r\n            if (\r\n                form_data?.[singleMeta.key] != prefill_data?.[singleMeta.key] &&\r\n                singleMeta?.type == 'Barcode_scanner'\r\n            ) {\r\n                returnFields[singleMeta.key] = form_data[singleMeta.key];\r\n            }\r\n        });\r\n        return returnFields;\r\n    };\r\n\r\n    getOriginalMetaFrDynamicForm() {\r\n        let custFieldJson = getCustomFieldsJsonFrStatus(\r\n            this.props.updateTypeId,\r\n            this.props.updateTypeDetails?.config_data\r\n        );\r\n        if (custFieldJson) {\r\n            custFieldJson = JSON.parse(custFieldJson)?.translatedFields;\r\n            // custFieldJson = decodeFieldsMetaFrmJson(custFieldJson,undefined,true,true,this.formRef);\r\n        }\r\n        return custFieldJson;\r\n    }\r\n\r\n    setIsExecutingDynamicFormLogic(value) {\r\n        this.setState({\r\n            isExecutingDynamicFormLogic: value,\r\n        });\r\n    }\r\n\r\n    // Helper method to check which draft fields are missing from current form values\r\n    getMissingDraftFields(draftData, currentFormValues) {\r\n        if (!draftData || !currentFormValues) return {};\r\n\r\n        const missingFields = {};\r\n        Object.keys(draftData).forEach((key) => {\r\n            // Skip file-related fields as they are handled separately\r\n            if (['attachments', 'mic_files', 'camera_files'].includes(key)) {\r\n                return;\r\n            }\r\n\r\n            const draftValue = draftData[key];\r\n            const currentValue = currentFormValues[key];\r\n\r\n            // Check if the field is missing or has different value\r\n            if (draftValue && !currentValue) {\r\n                missingFields[key] = draftValue;\r\n            }\r\n        });\r\n\r\n        return missingFields;\r\n    }\r\n\r\n    // Apply draft data and retry dynamic form logic if needed\r\n    applyDraftAndRetryIfNeeded(draftData, isRetryForDraft = false) {\r\n        // Check if draft functionality is enabled\r\n        if (!this.state.TMS250617437781) {\r\n            this.finalizeDraftApplication();\r\n            return;\r\n        }\r\n\r\n        const MAX_RETRY_ATTEMPTS = 5; // Prevent infinite loops\r\n        const currentFormValues = this.formRef.current.getFieldsValue();\r\n        console.log('currentFormValues', currentFormValues);\r\n        const missingFields = this.getMissingDraftFields(\r\n            draftData,\r\n            currentFormValues\r\n        );\r\n        console.log('missingFields', missingFields);\r\n\r\n        if (\r\n            Object.keys(missingFields).length > 0 &&\r\n            this.state.draftRetryCount < MAX_RETRY_ATTEMPTS\r\n        ) {\r\n            // Apply missing draft fields to the form\r\n            this.formRef.current.setFieldsValue(missingFields);\r\n\r\n            // Get updated form values after applying missing fields\r\n            const updatedFormValues = this.formRef.current.getFieldsValue();\r\n\r\n            console.log('updatedFormValues', updatedFormValues);\r\n\r\n            // Check if we still have missing fields after this application\r\n            const stillMissingFields = this.getMissingDraftFields(\r\n                draftData,\r\n                updatedFormValues\r\n            );\r\n\r\n            console.log('stillMissingFields', stillMissingFields);\r\n\r\n            if (Object.keys(stillMissingFields).length > 0) {\r\n                // We made progress, retry the dynamic form logic\r\n                console.log(\r\n                    'Retrying dynamic form logic to apply remaining draft fields:',\r\n                    stillMissingFields,\r\n                    'Attempt:',\r\n                    this.state.draftRetryCount + 1\r\n                );\r\n                this.setState({\r\n                    draftRetryCount: this.state.draftRetryCount + 1,\r\n                });\r\n                setTimeout(() => {\r\n                    this.executeDynamicFormLogic({}, updatedFormValues, true);\r\n                }, 100);\r\n            } else {\r\n                // No more progress can be made or all fields applied\r\n                this.finalizeDraftApplication();\r\n            }\r\n        } else {\r\n            // All draft fields are applied or max retries reached\r\n            this.finalizeDraftApplication();\r\n        }\r\n    }\r\n\r\n    // Finalize draft application and reset state\r\n    finalizeDraftApplication() {\r\n        this.setIsExecutingDynamicFormLogic(false);\r\n        this.setState({\r\n            isDynamicFormFirstExec: false,\r\n            draftRetryCount: 0, // Reset retry counter\r\n        });\r\n    }\r\n\r\n    executeDynamicFormLogic = (\r\n        changedValues,\r\n        allValues,\r\n        isRetryForDraft = false\r\n    ) => {\r\n        // Write a API call to TMS here\r\n        this.setIsExecutingDynamicFormLogic(true);\r\n        //Todo for dynamic form. Key sent so form can see if draft needs to be rendered\r\n        let renderingFrmDraft = false;\r\n        let draftData = null;\r\n        if (\r\n            (this.state.isDynamicFormFirstExec || isRetryForDraft) &&\r\n            this.state.TMS250617437781\r\n        ) {\r\n            // Check if there's draft data available for this dynamic form\r\n            draftData = DraftManager.applyDraft(this.draftParams(), true);\r\n            if (draftData && Object.keys(draftData).length > 0) {\r\n                renderingFrmDraft = true;\r\n            }\r\n        }\r\n        // message.warning(<Spin/>);// To be set by jainish\r\n\r\n        allValues['attachments'] = this.state.filesBySection;\r\n        allValues['mic_files'] = this.state.micRecordingsBySection;\r\n        allValues['camera_files'] = this.state.cameraRecordingsBySection;\r\n\r\n        const params = {\r\n            changedValues,\r\n            allValues,\r\n            meta: this.getOriginalMetaFrDynamicForm(),\r\n            currentMeta: this.state.manipulatedDynamicMeta,\r\n        };\r\n        // console.log('executeDynamicFormLogic params',params);\r\n        params['sbtsk_type_id'] = this.props.editorItem.sbtsk_type.value;\r\n        params['srvc_req_id'] = this.state?.viewData?.form_data?.srvc_req_id;\r\n        params['srvc_type_id'] = this.state?.viewData?.form_data?.srvc_type_id;\r\n        params['renderingFrmDraft'] = renderingFrmDraft;\r\n\r\n        http_utils.performPostCall(\r\n            dynamicFormLogicPath +\r\n                '/' +\r\n                this.props.editorItem.id +\r\n                '/' +\r\n                this.props.updateTypeId,\r\n            params,\r\n            (resp) => {\r\n                // console.log('executeDynamicFormLogic resp.data', resp.data);\r\n                const {\r\n                    meta,\r\n                    allValues,\r\n                    changedValues,\r\n                    manipulatedFieldValues,\r\n                    disableFormSubmissionButton,\r\n                    errorMessage,\r\n                } = resp.data.data;\r\n                if (errorMessage) {\r\n                    message.config({\r\n                        duration: errorMessage.duration || 3,\r\n                    });\r\n                    message.error(errorMessage.message);\r\n                }\r\n                this.setState(\r\n                    {\r\n                        manipulatedDynamicMeta: meta,\r\n                        disableSubmissionButton: disableFormSubmissionButton,\r\n                    },\r\n                    () => {\r\n                        // For files,mic,camera\r\n                        this.initConfigData(\r\n                            {\r\n                                data: this.state.viewData,\r\n                            },\r\n                            () => {\r\n                                this.formRef.current.setFieldsValue(\r\n                                    manipulatedFieldValues\r\n                                );\r\n\r\n                                // Apply draft data and retry if needed for dynamic forms\r\n                                if (\r\n                                    renderingFrmDraft &&\r\n                                    draftData &&\r\n                                    this.state.TMS250617437781\r\n                                ) {\r\n                                    this.applyDraftAndRetryIfNeeded(\r\n                                        draftData,\r\n                                        isRetryForDraft\r\n                                    );\r\n                                } else {\r\n                                    this.setIsExecutingDynamicFormLogic(false);\r\n                                    if (\r\n                                        !this.state.isDynamicFormFirstExec &&\r\n                                        !isRetryForDraft &&\r\n                                        this.state.TMS250617437781\r\n                                    ) {\r\n                                        DraftManager.updateDraft(\r\n                                            this.draftParams()\r\n                                        );\r\n                                    } else {\r\n                                        this.setState({\r\n                                            isDynamicFormFirstExec: false,\r\n                                        });\r\n                                    }\r\n                                }\r\n                            },\r\n                            true\r\n                        );\r\n                    }\r\n                );\r\n            },\r\n            (error) => {\r\n                console.log(\r\n                    'Error in onFormValueChange API call',\r\n                    http_utils.decodeErrorToMessage(error)\r\n                );\r\n                message.error('Auto form change not working!, Contact admin ');\r\n                this.setIsExecutingDynamicFormLogic(false);\r\n            }\r\n        );\r\n    };\r\n\r\n    createParamsFrBleExec() {\r\n        return {\r\n            sbtsk_type_id: this.props.editorItem?.sbtsk_type?.value,\r\n            srvc_req_id: this.state?.viewData?.form_data?.srvc_req_id,\r\n            srvc_type_id: this.state?.viewData?.form_data?.srvc_type_id,\r\n            entry_id: this.props.editorItem?.id,\r\n            update_type_id: this.props?.updateTypeId,\r\n            collab_order_id: this.state?.viewData?.form_data?.collab_order_id,\r\n        };\r\n    }\r\n\r\n    bleComponentProps() {\r\n        return {\r\n            taskUpdateVisible: this.state.updateClosureToChildComponent,\r\n            paramsFrLambdaExec: this.createParamsFrBleExec(),\r\n        };\r\n    }\r\n\r\n    onFormValueChanged = (changedValues, allValues) => {\r\n        if (debouncer) {\r\n            clearTimeout(debouncer);\r\n        }\r\n        debouncer = setTimeout(() => {\r\n            this.executeDynamicFormLogic(changedValues, allValues);\r\n        }, 750);\r\n    };\r\n    onCustomButtonClick = (fieldId) => {\r\n        if (this.isDynamicForm()) {\r\n            this.onFormValueChanged(\r\n                { [fieldId]: true },\r\n                this.formRef?.current?.getFieldsValue()\r\n            );\r\n        } else {\r\n            console.log('Not a dynamic form');\r\n        }\r\n    };\r\n\r\n    submitForm = (data) => {\r\n        this.setState({\r\n            isFormSubmitting: true,\r\n        });\r\n        let touchedFields = getTouchedFieldsValueInForm(\r\n            data,\r\n            this.formRef?.current,\r\n            this.getSpecificFieldsFrStatus().fields\r\n        );\r\n        touchedFields = {\r\n            ...touchedFields,\r\n            ...this.hasAnyBarcodeValueChanged(\r\n                data,\r\n                this.getSpecificFieldsFrStatus().fields,\r\n                this.state.viewData?.form_data?.form_data\r\n            ),\r\n        };\r\n        if (this.state.editMode) {\r\n            // compare files with prefill data\r\n            let isAnyFileChanged = hasAnyFileChanged(\r\n                this.state.filesBySection,\r\n                this.state.viewData?.form_data?.form_data?.attachments\r\n            );\r\n            if (isAnyFileChanged) {\r\n                touchedFields['attachments'] = this.state.filesBySection;\r\n            }\r\n\r\n            // compare files with prefill data\r\n            let isAnyMicFileChanged = hasAnyFileChanged(\r\n                this.state.micRecordingsBySection,\r\n                this.state.viewData?.form_data?.form_data?.mic_files\r\n            );\r\n            if (isAnyMicFileChanged) {\r\n                touchedFields['mic_files'] = this.state.micRecordingsBySection;\r\n            }\r\n\r\n            // compare camera files with prefill data\r\n            let isAnyCameraFileChanged = hasAnyFileChanged(\r\n                this.state.cameraRecordingsBySection,\r\n                this.state.viewData?.form_data?.form_data?.camera_files\r\n            );\r\n            if (isAnyCameraFileChanged) {\r\n                touchedFields['camera_files'] =\r\n                    this.state.cameraRecordingsBySection;\r\n            }\r\n            touchedFields['sbtsk_parts_consumption'] = this.state.partsData;\r\n        } else {\r\n            data['attachments'] = this.state.filesBySection;\r\n            data['mic_files'] = this.state.micRecordingsBySection;\r\n            data['camera_files'] = this.state.cameraRecordingsBySection;\r\n            data['sbtsk_parts_consumption'] = this.state.partsData;\r\n        }\r\n        if (this.state.TMS250617437781) {\r\n            let draftData = DraftManager.applyDraft(\r\n                this.draftParams(),\r\n                this.isDynamicForm()\r\n            );\r\n            if (draftData) {\r\n                touchedFields = _.merge(touchedFields, draftData);\r\n            }\r\n        }\r\n\r\n        var params = this.state.editMode ? touchedFields : data;\r\n        if (this.isDynamicForm()) {\r\n            data['attachments'] = this.state.filesBySection;\r\n            data['mic_files'] = this.state.micRecordingsBySection;\r\n            data['camera_files'] = this.state.cameraRecordingsBySection;\r\n            params = data;\r\n        }\r\n        if (Object.keys(params).length == 0) {\r\n            //if task updating with that updatetypeid which configured for multiple trigger then allow to update even nothing chnaged in form fields\r\n            const canTriggerUpdateWithoutChanges =\r\n                MyTaskConfigHelper.statusesToAllowFrMultipleTrigger(\r\n                    this.state.viewData?.sbtsk_config_data[0],\r\n                    this.props.updateTypeId\r\n                );\r\n            if (!canTriggerUpdateWithoutChanges) {\r\n                this.setState(\r\n                    {\r\n                        isFormSubmitting: false,\r\n                    },\r\n                    () => {\r\n                        message.info('No change in form');\r\n                    }\r\n                );\r\n                // Nothing to submit\r\n                return;\r\n            }\r\n        }\r\n        params['sbtsk_type_id'] = this.props.editorItem.sbtsk_type.value;\r\n        params['srvc_req_id'] = this.state?.viewData?.form_data?.srvc_req_id;\r\n        params['srvc_type_id'] = this.state?.viewData?.form_data?.srvc_type_id;\r\n        if (this.needToCaptureLocation()) {\r\n            params['user_gps_location_details'] = this.state.locationData;\r\n            params['user_gps_location_text'] =\r\n                this.state.locationData?.address?.text;\r\n            params['user_gps_location_status'] =\r\n                defaultLocationVerificationStatus;\r\n        }\r\n        const onComplete = (resp) => {\r\n            if (this.state.TMS250617437781) {\r\n                DraftManager.clearDraft(this.draftParams());\r\n            }\r\n            this.setState({\r\n                isFormSubmitting: false,\r\n                error: '',\r\n                visible: false,\r\n            });\r\n            this.tellParentToRefreshList(resp.entry_id);\r\n            this.updateClosureToParent();\r\n            showCreateOrUpdateSuccessMessage();\r\n        };\r\n        const onError = (error) => {\r\n            // compare statuses here\r\n            this.setState({\r\n                isFormSubmitting: false,\r\n                error: http_utils.decodeErrorToMessage(error),\r\n            });\r\n        };\r\n        http_utils.performPutCall(\r\n            submitUrl +\r\n                '/' +\r\n                this.props.editorItem.id +\r\n                '/' +\r\n                this.props.updateTypeId,\r\n            params,\r\n            onComplete,\r\n            onError\r\n        );\r\n    };\r\n\r\n    reGenerateOtp() {\r\n        this.setState({\r\n            isFormSubmitting: true,\r\n            otpRegenrated: true,\r\n            otp_expiry: Math.floor(Date.now()) + 30000,\r\n        });\r\n\r\n        let params = {};\r\n        params['reSendOtp'] = true;\r\n        params['sbtsk_db_id'] = this.props.editorItem.id;\r\n        const onComplete = (resp) => {\r\n            this.setState({\r\n                isFormSubmitting: false,\r\n            });\r\n        };\r\n        const onError = (error) => {\r\n            // compare statuses here\r\n            this.setState({\r\n                isFormSubmitting: false,\r\n                error: http_utils.decodeErrorToMessage(error),\r\n            });\r\n        };\r\n        http_utils.performGetCall(\r\n            reSendOtpUrl + '/' + this.state?.viewData?.form_data?.srvc_req_id,\r\n            params,\r\n            onComplete,\r\n            onError\r\n        );\r\n    }\r\n\r\n    onOTPCountDownFinish() {\r\n        this.setState({\r\n            render_helper: !this.state.render_helper,\r\n        });\r\n    }\r\n    getRemarkFieldFrMeta(configuredColSpan) {\r\n        return {\r\n            key: `remarks`,\r\n            colSpan: configuredColSpan,\r\n            label: 'Remarks',\r\n            widget: 'textarea',\r\n            rules: [\r\n                {\r\n                    required: true,\r\n                },\r\n            ],\r\n        };\r\n    }\r\n    handlePartsUpdate = (updatedItems) => {\r\n        this.setState({ partsData: updatedItems });\r\n\r\n        // DraftManager.updateDraft(\r\n        //     this.draftParams(),\r\n        //     //this.state.partsData,\r\n        //     updatedItems,\r\n        //     'sbtsk_parts_consumption'\r\n        // );\r\n    };\r\n    getRequestInfoMeta() {\r\n        const startTimeFrEndTime =\r\n            this.formRef.current?.getFieldValue('sbtsk_start_time');\r\n        const startOfDay =\r\n            this.state.viewData?.sbtsk_config_data[0]?.config_data\r\n                ?.sbtsk_time_slot_lower_limit || '09:00AM';\r\n        const endOfDay =\r\n            this.state.viewData?.sbtsk_config_data[0]?.config_data\r\n                ?.sbtsk_time_slot_upper_limit || '07:00PM';\r\n\r\n        let showOtpField =\r\n            this.state?.viewData?.form_data\r\n                ?.configure_consumer_otp_verification;\r\n        let doSrvcReqHasOtpInFormData =\r\n            this.state?.viewData?.form_data?.srvc_req_has_otp;\r\n        let seletedStatusForOtpVerification =\r\n            this.state?.viewData?.form_data\r\n                ?.selected_status_for_otp_verification;\r\n        let selectedSrvcTypeIdForOtpVerification =\r\n            this.state?.viewData?.form_data\r\n                ?.selected_srvc_type_id_for_otp_verification;\r\n        let srvc_type_id_of_sbtsk =\r\n            this.state?.viewData?.form_data?.srvc_type_id;\r\n        let otp_field_label = this.props?.editorItem?.otp_field_label || 'OTP';\r\n        const updateTypeDetails = this.getUpdateTypeDetails();\r\n        const statusConfiguredFrParts =\r\n            this.state.viewData?.sbtsk_config_data[0]?.config_data\r\n                ?.statues_for_parts_consumption || [];\r\n        let isStatusConfiguredFrParts = false;\r\n        if (statusConfiguredFrParts.includes(this.props.updateTypeId)) {\r\n            isStatusConfiguredFrParts = true;\r\n        }\r\n        const postponeFields = [\r\n            {\r\n                key: 'sbtsk_start_day',\r\n                label: (\r\n                    <b>\r\n                        <i className=\"icon icon-calendar gx-mr-2\"></i>Start date\r\n                    </b>\r\n                ),\r\n                widget: 'date-picker',\r\n                required: true,\r\n                colSpan: 3,\r\n                widgetProps: {\r\n                    disabledDate: disabledDate,\r\n                    style: {\r\n                        width: '100%',\r\n                    },\r\n                    onChange: (value, dateString) => {\r\n                        this.formRef.current.setFieldsValue({\r\n                            sbtsk_start_day: moment.utc(dateString),\r\n                        });\r\n                    },\r\n                },\r\n            },\r\n            {\r\n                key: 'label2',\r\n                colSpan: 4,\r\n                render() {\r\n                    return <></>;\r\n                },\r\n            },\r\n            {\r\n                key: 'sbtsk_start_time',\r\n                label: (\r\n                    <b>\r\n                        <i className=\"icon icon-timepicker gx-mr-2\"></i>Start\r\n                        Time\r\n                    </b>\r\n                ),\r\n                widget: TimePickerWidget,\r\n                required: true,\r\n                widgetProps: {\r\n                    beginLimit: startOfDay,\r\n                    endLimit: endOfDay,\r\n                    step: 15,\r\n                    onChange: (value) => {\r\n                        this.setState({\r\n                            render_helper: !this.state.render_helper,\r\n                        });\r\n                    },\r\n                },\r\n                colSpan: 2,\r\n            },\r\n            {\r\n                key: 'sbtsk_end_time',\r\n                label: (\r\n                    <b>\r\n                        <i className=\"icon icon-timepicker gx-mr-2\"></i>End Time\r\n                    </b>\r\n                ),\r\n                widget: TimePickerWidget,\r\n                required: true,\r\n                widgetProps: {\r\n                    beginLimit: startTimeFrEndTime\r\n                        ? startTimeFrEndTime\r\n                        : startOfDay,\r\n                    endLimit: endOfDay,\r\n                    step: 15,\r\n                },\r\n                colSpan: 2,\r\n            },\r\n        ];\r\n\r\n        let configuredColSpan =\r\n            this.props.updateTypeDetails?.config_data?.[\r\n                `subtask_status_${this.props.updateTypeId}_fields_colspan`\r\n            ] || 4;\r\n        const showRemarksAtBottom =\r\n            this.props.updateTypeDetails?.config_data?.[\r\n                `move_remarks_field_to_bottom_for_${this.props.updateTypeId}`\r\n            ];\r\n        const meta = {\r\n            columns: 1,\r\n            formItemLayout: null,\r\n            fields: [\r\n                //check\r\n                ...(isStatusConfiguredFrParts ? [this.getPartsData()] : []),\r\n                ...this.getSpecificFieldsFrStatus().fields,\r\n                ...(this.props.updateTypeId == 'sbtsk_can_postpone'\r\n                    ? postponeFields\r\n                    : []),\r\n                ...(!showRemarksAtBottom\r\n                    ? [this.getRemarkFieldFrMeta(configuredColSpan)]\r\n                    : []),\r\n            ],\r\n        };\r\n\r\n        if (\r\n            showOtpField &&\r\n            doSrvcReqHasOtpInFormData &&\r\n            updateTypeDetails?.value == seletedStatusForOtpVerification &&\r\n            selectedSrvcTypeIdForOtpVerification?.includes(\r\n                srvc_type_id_of_sbtsk\r\n            )\r\n        ) {\r\n            meta.fields.push(\r\n                {\r\n                    key: `otp`,\r\n                    colSpan: 4,\r\n                    label: `Enter ${otp_field_label}`,\r\n                    widget: 'input',\r\n                    rules: [\r\n                        {\r\n                            pattern: new RegExp('^[0-9]{4}$'),\r\n                            message: 'Please enter 4 digit number only!',\r\n                            required: true,\r\n                        },\r\n                    ],\r\n                },\r\n                {\r\n                    colSpan: 4,\r\n                    render: () => {\r\n                        return (\r\n                            <>\r\n                                {this.state.otp_expiry &&\r\n                                this.state.otp_expiry >\r\n                                    Math.floor(Date.now()) ? (\r\n                                    <span>\r\n                                        Retry in{' '}\r\n                                        <Countdown\r\n                                            className=\"gx-d-inline-block\"\r\n                                            title={null}\r\n                                            format=\"ss\"\r\n                                            value={this.state.otp_expiry}\r\n                                            onFinish={() => {\r\n                                                this.onOTPCountDownFinish();\r\n                                            }}\r\n                                        />\r\n                                    </span>\r\n                                ) : (\r\n                                    <Button\r\n                                        onClick={() => this.reGenerateOtp()}\r\n                                        type=\"primary\"\r\n                                        className=\"gx-btn-outline-info m-2\"\r\n                                    >\r\n                                        <UndoOutlined />{' '}\r\n                                        {`Resend ${otp_field_label}`}\r\n                                    </Button>\r\n                                )}\r\n                                {this.state.otpRegenrated && (\r\n                                    <p>\r\n                                        OTP has been sent to Customer Mobile no\r\n                                    </p>\r\n                                )}\r\n                            </>\r\n                        );\r\n                    },\r\n                }\r\n            );\r\n        }\r\n        if (showRemarksAtBottom) {\r\n            meta.fields.push(this.getRemarkFieldFrMeta(configuredColSpan));\r\n        }\r\n\r\n        return meta;\r\n    }\r\n\r\n    getPartsData() {\r\n        return {\r\n            key: 'sbtsk_parts_consumption',\r\n            colSpan: 1,\r\n            render: () => {\r\n                return (\r\n                    <>\r\n                        <PartsEditor\r\n                            itemEditorData={\r\n                                this.state.viewData?.form_data?.parts_data?.data\r\n                                    ?.data\r\n                            }\r\n                            onPartsUpdate={this.handlePartsUpdate}\r\n                            showPrice={\r\n                                this.state.viewData?.sbtsk_config_data[0]\r\n                                    ?.config_data?.show_part_price_to_assignee\r\n                            }\r\n                            formRef={this.formRef}\r\n                        />\r\n                    </>\r\n                );\r\n            },\r\n        };\r\n    }\r\n\r\n    isDynamicForm() {\r\n        return this.props.updateTypeDetails?.config_data?.[\r\n            `sbtsk_status_enable_dynamic_form_for_${this.props.updateTypeId}_status`\r\n        ];\r\n    }\r\n\r\n    getCustomFieldsJsonFrmConfig() {\r\n        if (this.state.manipulatedDynamicMeta) {\r\n            return JSON.stringify({\r\n                translatedFields: this.state.manipulatedDynamicMeta.filter(\r\n                    (singleFieldMeta) => singleFieldMeta.hide != true\r\n                ),\r\n            });\r\n        }\r\n        return getCustomFieldsJsonFrStatus(\r\n            this.props.updateTypeId,\r\n            this.props.updateTypeDetails?.config_data\r\n        );\r\n    }\r\n\r\n    getSpecificFieldsFrStatus() {\r\n        let customFields = decodeFieldsMetaFrmJson(\r\n            this.getCustomFieldsJsonFrmConfig(),\r\n            undefined,\r\n            false,\r\n            true,\r\n            this.formRef,\r\n            () => {\r\n                if (this.isDynamicForm()) {\r\n                    this.onFormValueChanged(\r\n                        {},\r\n                        this.formRef.current.getFieldsValue()\r\n                    );\r\n                } else {\r\n                    console.log('Not a dynamic form');\r\n                }\r\n            },\r\n            false,\r\n            this.bleComponentProps(),\r\n            (fieldId) => this.onCustomButtonClick(fieldId)\r\n        );\r\n        // console.log('Custom fields for status',customFields.map(fieldMeta=>{fieldMeta.colSpan=1; return fieldMeta}));\r\n        const meta = {\r\n            columns: 4,\r\n            formItemLayout: null,\r\n            fields:\r\n                customFields.length > 0\r\n                    ? [\r\n                          // {\r\n                          //     key: 'specific_details_fr_status',\r\n                          //     colSpan: 4,\r\n                          //     render() {\r\n                          //     return (\r\n                          //         <fieldset>\r\n                          //         <legend><b>Specific details</b></legend>\r\n                          //         </fieldset>\r\n                          //     )\r\n                          //     },\r\n                          // },\r\n                          ...customFields.map((fieldMeta) => {\r\n                              fieldMeta.colSpan = fieldMeta.colSpan || 4;\r\n                              return fieldMeta;\r\n                          }),\r\n                      ]\r\n                    : [],\r\n        };\r\n        return meta;\r\n    }\r\n\r\n    getUpdateTypeDetails() {\r\n        let updateTypeId = this.props.updateTypeId;\r\n        let statuses = this.props.updateTypeDetails?.statuses;\r\n        let returnUpdateTypeDetails;\r\n        if (statuses)\r\n            statuses.map((singleStatus) => {\r\n                if (singleStatus.value == updateTypeId) {\r\n                    returnUpdateTypeDetails = singleStatus;\r\n                }\r\n            });\r\n        return returnUpdateTypeDetails;\r\n    }\r\n\r\n    onFilesChanged(section, files, uploadedFiles) {\r\n        let newFilesBySection = this.state.filesBySection;\r\n        newFilesBySection[section] = files;\r\n        //removing state as we are handling this with draft\r\n        // this.setState({\r\n        //     filesBySection : newFilesBySection\r\n        // })\r\n        let uploadedFilesBySection = this.state.uploadedFilesBySection;\r\n        uploadedFilesBySection[section] = uploadedFiles;\r\n\r\n        this.setState({\r\n            uploadedFilesBySection,\r\n        });\r\n        if (this.state.TMS250617437781) {\r\n            let attachments = newFilesBySection;\r\n            DraftManager.updateDraft(\r\n                this.draftParams(),\r\n                attachments,\r\n                'attachments'\r\n            );\r\n        }\r\n    }\r\n\r\n    onMicFilesChanged(section, files) {\r\n        let newFilesBySection = this.state.micRecordingsBySection;\r\n        newFilesBySection[section] = files;\r\n        //removing state as we are handling this with draft\r\n        // this.setState({\r\n        //     micRecordingsBySection : newFilesBySection\r\n        // })\r\n        if (this.state.TMS250617437781) {\r\n            let micFiles = newFilesBySection;\r\n            DraftManager.updateDraft(this.draftParams(), micFiles, 'mic_files');\r\n        }\r\n    }\r\n\r\n    onCameraFilesChanged(section, files) {\r\n        let newFilesBySection = this.state.cameraRecordingsBySection;\r\n        newFilesBySection[section] = files;\r\n        //removing state as we are handling this with draft\r\n        // this.setState({\r\n        //     cameraRecordingsBySection : newFilesBySection\r\n        // })\r\n        if (this.state.TMS250617437781) {\r\n            let cameraFiles = newFilesBySection;\r\n            DraftManager.updateDraft(\r\n                this.draftParams(),\r\n                cameraFiles,\r\n                'camera_files'\r\n            );\r\n        }\r\n    }\r\n\r\n    onFileUploaderReadyChange(section, isReady) {\r\n        let newSectionWiseReady = this.state.sectionWiseUploaderReady;\r\n        newSectionWiseReady[section] = isReady;\r\n        this.setState({\r\n            sectionWiseUploaderReady: newSectionWiseReady,\r\n        });\r\n    }\r\n\r\n    onMicFileUploaderReadyChange(section, isReady) {\r\n        let newSectionWiseReady = this.state.sectionWiseMicUploaderReady;\r\n        newSectionWiseReady[section] = isReady;\r\n        this.setState({\r\n            sectionWiseMicUploaderReady: newSectionWiseReady,\r\n        });\r\n    }\r\n\r\n    onCameraFileUploaderReadyChange(section, isReady) {\r\n        let newSectionWiseReady = this.state.sectionWiseCameraUploaderReady;\r\n        newSectionWiseReady[section] = isReady;\r\n        this.setState({\r\n            sectionWiseCameraUploaderReady: newSectionWiseReady,\r\n        });\r\n    }\r\n\r\n    getAllFileUploadersReady() {\r\n        let {\r\n            sectionWiseUploaderReady,\r\n            sectionWiseMicUploaderReady,\r\n            sectionWiseCameraUploaderReady,\r\n        } = this.state;\r\n        let notReady = false;\r\n        Object.keys(sectionWiseUploaderReady).map((section) => {\r\n            if (!sectionWiseUploaderReady[section]) {\r\n                notReady = true;\r\n            }\r\n        });\r\n        Object.keys(sectionWiseMicUploaderReady).map((section) => {\r\n            if (!sectionWiseMicUploaderReady[section]) {\r\n                notReady = true;\r\n            }\r\n        });\r\n        Object.keys(sectionWiseCameraUploaderReady).map((section) => {\r\n            if (!sectionWiseCameraUploaderReady[section]) {\r\n                notReady = true;\r\n            }\r\n        });\r\n        return !notReady;\r\n    }\r\n\r\n    draftParams = () => {\r\n        return {\r\n            sbtsk_id: this.props?.editorItem?.id,\r\n            update_type_id: this.props?.updateTypeId,\r\n            state: {\r\n                filesBySection: this.state.filesBySection,\r\n                micRecordingsBySection: this.state.micRecordingsBySection,\r\n                cameraRecordingsBySection: this.state.cameraRecordingsBySection,\r\n            },\r\n            fieldValues: this.formRef?.current?.getFieldsValue(),\r\n            isDynamicForm: this.isDynamicForm(),\r\n        };\r\n    };\r\n\r\n    removeUploadedFilesFromDraft = (draft, uploadedFiles) => {\r\n        const newDraft = JSON.parse(JSON.stringify(draft)); // Create a deep copy of draft\r\n\r\n        for (const key in newDraft.attachments) {\r\n            if (newDraft.attachments.hasOwnProperty(key)) {\r\n                newDraft.attachments[key] = newDraft.attachments[key].filter(\r\n                    (file) => {\r\n                        return !uploadedFiles[key]?.includes(file);\r\n                    }\r\n                );\r\n            }\r\n        }\r\n        return newDraft;\r\n    };\r\n\r\n    render() {\r\n        const { editorItem } = this.props;\r\n        const {\r\n            isFormSubmitting,\r\n            visible,\r\n            isLoadingViewData,\r\n            error,\r\n            viewData,\r\n            currentStep,\r\n            srvcDetails,\r\n            fileSections,\r\n            micSections,\r\n            cameraSections,\r\n            bleSections,\r\n            isLoadingLocation,\r\n            locationData,\r\n            isExecutingDynamicFormLogic,\r\n            disableSubmissionButton,\r\n        } = this.state;\r\n        // console.log(\"Task update editor props - \",this.props);\r\n        const updateTypeDetails = this.getUpdateTypeDetails();\r\n\r\n        const mandatoryStatusReq =\r\n            viewData?.form_data?.mandatory_status == null\r\n                ? []\r\n                : viewData?.form_data?.mandatory_status;\r\n\r\n        let editorTitle = 'Update task';\r\n        let editorColor = '#e1e1e1';\r\n        let isFormFilledByDraft = false;\r\n        let draftData = null;\r\n        // For dynamic forms, force apply draft data to get it before dynamic logic executes\r\n        if (this.state.TMS250617437781) {\r\n            draftData = this.isDynamicForm()\r\n                ? DraftManager.applyDraft(this.draftParams(), true)\r\n                : DraftManager.applyDraft(this.draftParams());\r\n            if (draftData && Object.keys(draftData).length > 0) {\r\n                isFormFilledByDraft = true;\r\n            }\r\n        }\r\n        let updatedDraftData = {\r\n            ...this.state.viewData?.form_data?.form_data,\r\n            ...draftData,\r\n        };\r\n        updatedDraftData = this.removeUploadedFilesFromDraft(\r\n            updatedDraftData,\r\n            this.state.uploadedFilesBySection\r\n        );\r\n        let prefillFormData = convertDateFieldsToMoments(\r\n            updatedDraftData,\r\n            this.getRequestInfoMeta().fields\r\n        );\r\n        const allFileUploadersReady = this.getAllFileUploadersReady();\r\n\r\n        if (updateTypeDetails) {\r\n            editorTitle = 'Update task as ' + updateTypeDetails.title;\r\n            editorColor = updateTypeDetails.color;\r\n        } else if (\r\n            this.props.updateTypeId == 'sbtsk_can_postpone' ||\r\n            this.props.updateTypeId == 'sbtsk_can_reject'\r\n        ) {\r\n            editorTitle =\r\n                this.props.updateTypeId == 'sbtsk_can_postpone'\r\n                    ? 'Postpone task'\r\n                    : 'Reject task';\r\n            editorColor =\r\n                this.props.updateTypeId == 'sbtsk_can_postpone'\r\n                    ? '#13c2c2'\r\n                    : '#f5222d';\r\n            let attachments = prefillFormData?.attachments;\r\n            prefillFormData = {};\r\n            prefillFormData['attachments'] = attachments ? attachments : {};\r\n        }\r\n        if (!this.state.TMS250523533568) {\r\n            setRemarkFieldAsNoRemarkIfEmpty(prefillFormData, this.formRef);\r\n        }\r\n\r\n        return (\r\n            <Modal\r\n                title={\r\n                    <span>\r\n                        <i\r\n                            style={{ color: editorColor }}\r\n                            className={`icon icon-circle gx-mr-2 gx-mt-2 gx-vertical-align-middle`}\r\n                        />\r\n                        {editorTitle}\r\n                    </span>\r\n                }\r\n                visible={visible}\r\n                onOk={this.handleOk}\r\n                confirmLoading={isFormSubmitting}\r\n                width={1500}\r\n                style={{\r\n                    marginTop: '-70px',\r\n                }}\r\n                bodyStyle={{\r\n                    minHeight: '85vh',\r\n                    padding: '18px',\r\n                    paddingTop: '0px',\r\n                }}\r\n                footer={null}\r\n                onCancel={this.handleCancel}\r\n                afterClose={\r\n                    this.state.TMS250617437781 &&\r\n                    DraftManager.applyDraft(\r\n                        this.draftParams(),\r\n                        this.isDynamicForm()\r\n                    )\r\n                        ? this.showDraftSavedPopUp\r\n                        : null\r\n                }\r\n            >\r\n                {isLoadingViewData ? (\r\n                    <div className=\"gx-loader-view gx-loader-position\">\r\n                        <CircularProgress />\r\n                    </div>\r\n                ) : viewData == undefined ? (\r\n                    <p className=\"gx-text-red\">{error}</p>\r\n                ) : (\r\n                    <>\r\n                        <Row className=\"gx-mt-2\">\r\n                            <Col\r\n                                xs={24}\r\n                                md={14}\r\n                                className=\"gx-border-right gx-mt-2\"\r\n                            >\r\n                                {mandatoryStatusReq.length > 0 ? (\r\n                                    <div>\r\n                                        <h5 className=\"gx-text-danger\">\r\n                                            Please update following statuses\r\n                                            first -{' '}\r\n                                            {this.mandatoryStatusRequirement(\r\n                                                mandatoryStatusReq\r\n                                            )}\r\n                                        </h5>\r\n                                    </div>\r\n                                ) : (\r\n                                    <></>\r\n                                )}\r\n\r\n                                <div className=\"gx-no-header-horizontal-top gx-d-block gx-py-2 gx-px-3\">\r\n                                    <div className=\" gx-text-grey\">\r\n                                        <div className=\"gx-mb-2\">\r\n                                            Assigned by - {editorItem.c_by} -{' '}\r\n                                            <span>\r\n                                                <TaskTime item={editorItem} />\r\n                                            </span>\r\n                                        </div>\r\n                                        {editorItem.title}\r\n                                    </div>\r\n                                    <div className=\"gx-ml-2 gx-border-left gx-border-grey \">\r\n                                        <Collapse\r\n                                            ghost\r\n                                            // expandIconPosition=\"right\"\r\n                                        >\r\n                                            <Collapse.Panel\r\n                                                header={\r\n                                                    editorItem.srvc_req_details\r\n                                                        .description\r\n                                                }\r\n                                            >\r\n                                                <div>\r\n                                                    <TaskSummaryView\r\n                                                        item={editorItem}\r\n                                                        sbtsk_config_data={\r\n                                                            this.state.viewData\r\n                                                                ?.sbtsk_config_data\r\n                                                                .length > 0\r\n                                                                ? this.state\r\n                                                                      .viewData\r\n                                                                      ?.sbtsk_config_data[0]\r\n                                                                : {}\r\n                                                        }\r\n                                                    />\r\n                                                </div>\r\n                                            </Collapse.Panel>\r\n                                        </Collapse>\r\n                                    </div>\r\n                                </div>\r\n                                {isFormFilledByDraft && (\r\n                                    <Alert\r\n                                        className=\"gx-mt-4\"\r\n                                        message=\"Unsaved changes, submit form to apply changes\"\r\n                                        type=\"error\"\r\n                                        showIcon\r\n                                    />\r\n                                )}\r\n                                <Form\r\n                                    // {...formItemLayout}\r\n                                    className=\"gx-w-100 gx-mt-3\"\r\n                                    layout=\"vertical\"\r\n                                    initialValues={prefillFormData}\r\n                                    ref={this.formRef}\r\n                                    onFinish={(data) => {\r\n                                        if (this.state.TMS250617437781) {\r\n                                            DraftManager.updateDraft(\r\n                                                this.draftParams()\r\n                                            );\r\n                                        }\r\n                                        this.submitForm(data);\r\n                                    }}\r\n                                    disabled={isExecutingDynamicFormLogic}\r\n                                    onValuesChange={(\r\n                                        changedValues,\r\n                                        allValues\r\n                                    ) => {\r\n                                        if (this.isDynamicForm()) {\r\n                                            this.onFormValueChanged(\r\n                                                changedValues,\r\n                                                allValues\r\n                                            );\r\n                                        } else {\r\n                                            if (this.state.TMS250617437781) {\r\n                                                DraftManager.updateDraft(\r\n                                                    this.draftParams()\r\n                                                );\r\n                                            }\r\n                                            console.log('Not a dynamic form');\r\n                                        }\r\n                                    }}\r\n                                >\r\n                                    {isExecutingDynamicFormLogic && (\r\n                                        <>\r\n                                            <div className=\"spin_progress\">\r\n                                                <Spin />\r\n                                            </div>\r\n                                        </>\r\n                                    )}\r\n                                    <Row\r\n                                        style={{\r\n                                            flexDirection: 'inherit',\r\n                                        }}\r\n                                    >\r\n                                        <Col xs={24} className=\"gx-pl-0\">\r\n                                            <FormBuilder\r\n                                                key=\"info\"\r\n                                                meta={this.getRequestInfoMeta()}\r\n                                                form={this.formRef}\r\n                                            />\r\n                                        </Col>\r\n                                        <Col\r\n                                            xs={24}\r\n                                            md={24}\r\n                                            className=\"gx-pl-0\"\r\n                                        >\r\n                                            {micSections.map(\r\n                                                (singleMicSection, index) => (\r\n                                                    <Col\r\n                                                        xs={24}\r\n                                                        md={24}\r\n                                                        className=\"gx-pl-0\"\r\n                                                        key={\r\n                                                            singleMicSection.key\r\n                                                        }\r\n                                                    >\r\n                                                        {singleMicSection.title !=\r\n                                                            '' && (\r\n                                                            <h3 className=\"gx-mt-3\">\r\n                                                                {\r\n                                                                    singleMicSection.title\r\n                                                                }\r\n                                                                <hr className=\"gx-bg-dark\"></hr>\r\n                                                            </h3>\r\n                                                        )}\r\n                                                        {/* For debugging */}\r\n                                                        {/* {\r\n                                                                this.state.micRecordingsBySection && \r\n                                                                <Alert \r\n                                                                    message={JSON.stringify(prefillFormData.mic_files?.[singleMicSection.key])}\r\n                                                                    type=\"info\" />\r\n                                                            } */}\r\n                                                        <MicInputV2\r\n                                                            authToken={http_utils.getAuthToken()}\r\n                                                            prefixDomain={http_utils.getCDNDomain()}\r\n                                                            initialFiles={\r\n                                                                this.state\r\n                                                                    .editMode\r\n                                                                    ? prefillFormData\r\n                                                                          ?.mic_files?.[\r\n                                                                          singleMicSection\r\n                                                                              .key\r\n                                                                      ]\r\n                                                                    : []\r\n                                                            }\r\n                                                            onFilesChanged={(\r\n                                                                files\r\n                                                            ) => {\r\n                                                                this.onMicFilesChanged(\r\n                                                                    singleMicSection.key,\r\n                                                                    files\r\n                                                                );\r\n                                                            }}\r\n                                                            onReadyStatusChange={(\r\n                                                                isReady\r\n                                                            ) => {\r\n                                                                this.onMicFileUploaderReadyChange(\r\n                                                                    singleMicSection.key,\r\n                                                                    isReady\r\n                                                                );\r\n                                                            }}\r\n                                                        />\r\n                                                    </Col>\r\n                                                )\r\n                                            )}\r\n                                        </Col>\r\n                                        <Col\r\n                                            xs={24}\r\n                                            md={24}\r\n                                            className=\"gx-pl-0\"\r\n                                        >\r\n                                            {cameraSections.map(\r\n                                                (\r\n                                                    singleCameraSection,\r\n                                                    index\r\n                                                ) => (\r\n                                                    <Col\r\n                                                        xs={24}\r\n                                                        md={24}\r\n                                                        className=\"gx-pl-0\"\r\n                                                        key={\r\n                                                            singleCameraSection.key\r\n                                                        }\r\n                                                    >\r\n                                                        {singleCameraSection.title !=\r\n                                                            '' && (\r\n                                                            <>\r\n                                                                <h3 className=\"gx-mt-3\">\r\n                                                                    {singleCameraSection.required && (\r\n                                                                        <span\r\n                                                                            style={{\r\n                                                                                color: 'red',\r\n                                                                            }}\r\n                                                                        >\r\n                                                                            {' '}\r\n                                                                            *{' '}\r\n                                                                        </span>\r\n                                                                    )}\r\n                                                                    {\r\n                                                                        singleCameraSection.title\r\n                                                                    }\r\n                                                                    <hr className=\"gx-bg-dark\"></hr>\r\n                                                                </h3>\r\n                                                            </>\r\n                                                        )}\r\n                                                        {/* For debugging */}\r\n                                                        {/* {\r\n                                                                this.state.micRecordingsBySection && \r\n                                                                <Alert \r\n                                                                    message={JSON.stringify(prefillFormData.mic_files?.[singleMicSection.key])}\r\n                                                                    type=\"info\" />\r\n                                                            } */}\r\n                                                        <CameraInput\r\n                                                            ref={this.cameraRef}\r\n                                                            authToken={http_utils.getAuthToken()}\r\n                                                            prefixDomain={http_utils.getCDNDomain()}\r\n                                                            required={\r\n                                                                singleCameraSection.required\r\n                                                            }\r\n                                                            initialFiles={\r\n                                                                this.state\r\n                                                                    .editMode\r\n                                                                    ? prefillFormData\r\n                                                                          ?.camera_files?.[\r\n                                                                          singleCameraSection\r\n                                                                              .key\r\n                                                                      ]\r\n                                                                    : []\r\n                                                            }\r\n                                                            onFilesChanged={(\r\n                                                                files\r\n                                                            ) => {\r\n                                                                this.onCameraFilesChanged(\r\n                                                                    singleCameraSection.key,\r\n                                                                    files\r\n                                                                );\r\n                                                            }}\r\n                                                            onReadyStatusChange={(\r\n                                                                isReady\r\n                                                            ) => {\r\n                                                                this.onCameraFileUploaderReadyChange(\r\n                                                                    singleCameraSection.key,\r\n                                                                    isReady\r\n                                                                );\r\n                                                            }}\r\n                                                        />\r\n                                                    </Col>\r\n                                                )\r\n                                            )}\r\n                                        </Col>\r\n                                        <Col\r\n                                            xs={24}\r\n                                            md={24}\r\n                                            className=\"gx-pl-0\"\r\n                                        >\r\n                                            {fileSections.map(\r\n                                                (singleFileSection, index) => (\r\n                                                    <Col\r\n                                                        xs={24}\r\n                                                        md={24}\r\n                                                        className=\"gx-pl-0\"\r\n                                                        key={\r\n                                                            singleFileSection.key\r\n                                                        }\r\n                                                    >\r\n                                                        {/* {singleFileSection.title != '' && <p>{singleFileSection.title}</p>} */}\r\n                                                        {singleFileSection.title !=\r\n                                                            '' && (\r\n                                                            <h3 className=\"gx-mt-3\">\r\n                                                                {singleFileSection.required && (\r\n                                                                    <span\r\n                                                                        style={{\r\n                                                                            color: 'red',\r\n                                                                        }}\r\n                                                                    >\r\n                                                                        {' '}\r\n                                                                        *{' '}\r\n                                                                    </span>\r\n                                                                )}\r\n                                                                {\r\n                                                                    singleFileSection.title\r\n                                                                }\r\n                                                                <hr className=\"gx-bg-dark\"></hr>\r\n                                                            </h3>\r\n                                                        )}\r\n                                                        {/* For debugging */}\r\n                                                        {/* {\r\n                                                                this.state.filesBySection && \r\n                                                                <Alert \r\n                                                                    message={JSON.stringify(this.state.filesBySection)}\r\n                                                                    type=\"info\" />\r\n                                                            } */}\r\n                                                        <S3Uploader\r\n                                                            // className=\"gx-w-50\"\r\n                                                            // demoMode\r\n                                                            required={\r\n                                                                singleFileSection.required\r\n                                                            }\r\n                                                            maxColSpan={4}\r\n                                                            authToken={http_utils.getAuthToken()}\r\n                                                            prefixDomain={http_utils.getCDNDomain()}\r\n                                                            onFilesChanged={(\r\n                                                                files,\r\n                                                                deletedFileUrl,\r\n                                                                uploadedFiles\r\n                                                            ) => {\r\n                                                                this.onFilesChanged(\r\n                                                                    singleFileSection.key,\r\n                                                                    files,\r\n                                                                    uploadedFiles\r\n                                                                );\r\n                                                            }}\r\n                                                            onReadyStatusChanged={(\r\n                                                                isReady\r\n                                                            ) => {\r\n                                                                this.onFileUploaderReadyChange(\r\n                                                                    singleFileSection.key,\r\n                                                                    isReady\r\n                                                                );\r\n                                                            }}\r\n                                                            initialFiles={\r\n                                                                this.state\r\n                                                                    .editMode\r\n                                                                    ? prefillFormData\r\n                                                                          ?.attachments?.[\r\n                                                                          singleFileSection\r\n                                                                              .key\r\n                                                                      ]\r\n                                                                    : []\r\n                                                            }\r\n                                                            isDraftEnabled\r\n                                                            customPreviewHeight=\"100%\"\r\n                                                            customFileIconMaxWidth=\"40px\"\r\n                                                            compConfig={{\r\n                                                                name: 'task-update-editor-attachments',\r\n                                                            }}\r\n                                                        />\r\n                                                    </Col>\r\n                                                )\r\n                                            )}\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <div className=\"gx-mt-4\">\r\n                                        {this.needToCaptureLocation() && (\r\n                                            <LocationDetails\r\n                                                isLoadingLocation={\r\n                                                    isLoadingLocation\r\n                                                }\r\n                                                locationData={locationData}\r\n                                                onRetry={() => {\r\n                                                    this.initGeoFenceStuff();\r\n                                                }}\r\n                                            />\r\n                                        )}\r\n                                    </div>\r\n                                    <div className=\"gx-mt-4\">\r\n                                        {!allFileUploadersReady && (\r\n                                            <Spin></Spin>\r\n                                        )}\r\n                                        <Button\r\n                                            type=\"primary\"\r\n                                            htmlType=\"submit\"\r\n                                            data-testid=\"submit-button\"\r\n                                            disabled={\r\n                                                isFormSubmitting ||\r\n                                                !allFileUploadersReady ||\r\n                                                mandatoryStatusReq.length > 0 ||\r\n                                                disableSubmissionButton ||\r\n                                                isExecutingDynamicFormLogic\r\n                                            }\r\n                                        >\r\n                                            {editorTitle}\r\n                                        </Button>\r\n\r\n                                        {isFormSubmitting ? (\r\n                                            <div className=\"gx-loader-view gx-loader-position\">\r\n                                                <CircularProgress />\r\n                                            </div>\r\n                                        ) : null}\r\n                                        {error ? (\r\n                                            <p className=\"gx-text-red\">\r\n                                                {error}\r\n                                            </p>\r\n                                        ) : null}\r\n                                    </div>\r\n                                </Form>\r\n                            </Col>\r\n                        </Row>\r\n                    </>\r\n                )}\r\n            </Modal>\r\n        );\r\n    }\r\n}\r\n\r\nexport default TaskUpdateEditor;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SACIC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,IAAI,EACJC,KAAK,QACF,MAAM;AACb,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,SACIC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,cAAc,EACdC,YAAY,QACT,mBAAmB;AAC1B;AACA,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,kBAAkB,MAAM,gDAAgD;AAC/E,SACIC,0BAA0B,EAC1BC,uBAAuB,EACvBC,gBAAgB,EAChBC,YAAY,EACZC,UAAU,EACVC,qBAAqB,EACrBC,2BAA2B,EAC3BC,iBAAiB,EACjBC,YAAY,EACZC,YAAY,EACZC,+BAA+B,EAC/BC,gCAAgC,QAC7B,oBAAoB;AAC3B,OAAOC,eAAe,MAAM,gDAAgD;AAC5E,OAAOC,QAAQ,MAAM,yCAAyC;AAC9D,OAAOC,gBAAgB,MAAM,8CAA8C;AAC3E,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAOC,UAAU,MAAM,mDAAmD;AAC1E,SACIC,uBAAuB,EACvBC,yBAAyB,EACzBC,wBAAwB,EACxBC,2BAA2B,EAC3BC,wBAAwB,QACrB,kDAAkD;AACzD,SAASC,2BAA2B,QAAQ,uBAAuB;AACnE,SAASC,OAAO,QAAQ,QAAQ;AAChC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,iCAAiC,QAAQ,+BAA+B;AACjF,OAAOC,UAAU,MAAM,yCAAyC;AAChE,OAAOC,WAAW,MAAM,yCAAyC;AACjE,OAAOC,SAAS,MAAM,8BAA8B;AACpD,OAAOC,uBAAuB,MAAM,wDAAwD;AAC5F,OAAOC,CAAC,MAAM,QAAQ;AACtB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,kBAAkB,MAAM,0BAA0B;;AAEzD;AACA;;AAEA,MAAMC,QAAQ,GAAG,iBAAiB;AAClC,MAAMC,SAAS,GAAG,WAAW;AAC7B;AACA,MAAMC,YAAY,GAAG,uBAAuB;AAC5C,MAAMC,oBAAoB,GAAG,mCAAmC;AAChE,IAAIC,SAAS;AAEb,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC3B;EACA,OAAOA,OAAO,IAAIA,OAAO,GAAG1B,MAAM,CAAC,CAAC,CAAC2B,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC;AACzE;AAEA,MAAMC,gBAAgB,SAAS9E,SAAS,CAAC;EACrC+E,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAACA,KAAK,CAAC;IAAC,KAKjBC,SAAS,GAAG;MACRC,aAAa,EAAE,KAAK;MACpBC,OAAO,EAAE,KAAK;MACdC,gBAAgB,EAAE,KAAK;MACvBC,QAAQ,EAAEC,SAAS;MACnBC,iBAAiB,EAAE,KAAK;MACxBC,QAAQ,EAAE,IAAI,CAACR,KAAK,CAACQ,QAAQ;MAC7BC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,CAAC;MACd;MACAC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,CAAC,CAAC;MAClBC,wBAAwB,EAAE,CAAC,CAAC;MAC5B;MACAC,WAAW,EAAE,EAAE;MACfC,sBAAsB,EAAE,CAAC,CAAC;MAC1BC,2BAA2B,EAAE,CAAC,CAAC;MAC/B;MACAC,cAAc,EAAE,EAAE;MAClBC,yBAAyB,EAAE,CAAC,CAAC;MAC7BC,8BAA8B,EAAE,CAAC,CAAC;MAClC;MACAC,iBAAiB,EAAE,KAAK;MACxBC,YAAY,EAAEf,SAAS;MACvB;MACAgB,aAAa,EAAE,KAAK;MACpB;MACAC,sBAAsB,EAAEjB,SAAS;MACjCkB,2BAA2B,EAAE,KAAK;MAClCC,uBAAuB,EAAE,KAAK;MAC9BC,6BAA6B,EAAE,IAAI;MACnCC,sBAAsB,EAAE,IAAI;MAC5BC,sBAAsB,EAAE,CAAC,CAAC;MAC1BC,SAAS,EAAE,EAAE;MACbC,eAAe,EAAE,CAAC,CAAE;IACxB,CAAC;IAAA,KAEDC,KAAK,GAAG,IAAI,CAAC9B,SAAS;IAAA,KAOtB+B,mBAAmB,GAAG,YAAY;MAC9B,IAAI;QACA,IAAIC,SAAS,GAAG,MAAM7C,kBAAkB,CAAC,iBAAiB,CAAC;QAC3D,IAAI,CAAC8C,QAAQ,CAAC;UAAEC,eAAe,EAAEF;QAAU,CAAC,CAAC;MACjD,CAAC,CAAC,OAAOxB,KAAK,EAAE;QACZ,IAAI,CAACyB,QAAQ,CAAC;UAAEC,eAAe,EAAE;QAAM,CAAC,CAAC;QACzCC,OAAO,CAAC3B,KAAK,CACT,qDAAqD,EACrDA,KACJ,CAAC;MACL;;MAEA;MACA,IAAI;QACA,IAAI4B,cAAc,GAAG,MAAMjD,kBAAkB,CAAC,iBAAiB,CAAC;QAChE,IAAI,CAAC8C,QAAQ,CAAC;UAAEI,eAAe,EAAED;QAAe,CAAC,CAAC;MACtD,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACZ,IAAI,CAACyB,QAAQ,CAAC;UAAEI,eAAe,EAAE;QAAM,CAAC,CAAC;QACzCF,OAAO,CAAC3B,KAAK,CACT,wEAAwE,EACxEA,KACJ,CAAC;MACL;IACJ,CAAC;IAAA,KAiPD8B,QAAQ,GAAG,MAAM;MACb,IAAI,CAACL,QAAQ,CAAC;QACV/B,OAAO,EAAE,KAAK;QACdC,gBAAgB,EAAE;MACtB,CAAC,CAAC;MACF,IAAI,CAACoC,qBAAqB,CAAC,CAAC;IAChC,CAAC;IAAA,KAqCDC,YAAY,GAAG,MAAM;MACjB,IAAI,CAACP,QAAQ,CACT;QACIR,6BAA6B,EAAE;MACnC,CAAC,EACD,MAAM;QAAA,IAAAgB,eAAA,EAAAC,qBAAA;QACF,IAAI,CAACT,QAAQ,CAAC;UACV/B,OAAO,EAAE;QACb,CAAC,CAAC;QACF,IAAI,CAACyC,wBAAwB,CAAC,CAAC;QAC/B,KAAAF,eAAA,GAAI,IAAI,CAACG,SAAS,cAAAH,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgB/C,OAAO,cAAAgD,qBAAA,uBAAvBA,qBAAA,CAAyBG,aAAa,EAAE;UACxC,IAAI,CAACD,SAAS,CAAClD,OAAO,CAACmD,aAAa,CAAC,CAAC;QAC1C;QACA,IAAI,CAACN,qBAAqB,CAAC,CAAC;QAC5B,IAAI,CAACN,QAAQ,CAAC;UACVN,sBAAsB,EAAE,CAAC;QAC7B,CAAC,CAAC;MACN,CACJ,CAAC;IACL,CAAC;IAAA,KAYDmB,yBAAyB,GAAG,CAACC,SAAS,EAAEC,SAAS,EAAEC,YAAY,KAAK;MAChE,IAAIC,YAAY,GAAG,CAAC,CAAC;MAErBF,SAAS,CAACG,OAAO,CAAEC,UAAU,IAAK;QAC9B,IACI,CAAAL,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGK,UAAU,CAACC,GAAG,CAAC,MAAIJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAGG,UAAU,CAACC,GAAG,CAAC,KAC7D,CAAAD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEE,IAAI,KAAI,iBAAiB,EACvC;UACEJ,YAAY,CAACE,UAAU,CAACC,GAAG,CAAC,GAAGN,SAAS,CAACK,UAAU,CAACC,GAAG,CAAC;QAC5D;MACJ,CAAC,CAAC;MACF,OAAOH,YAAY;IACvB,CAAC;IAAA,KAiHDK,uBAAuB,GAAG,CACtBC,aAAa,EACbC,SAAS,EACTC,eAAe,GAAG,KAAK,KACtB;MAAA,IAAAC,WAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACD;MACA,IAAI,CAACC,8BAA8B,CAAC,IAAI,CAAC;MACzC;MACA,IAAIC,iBAAiB,GAAG,KAAK;MAC7B,IAAIC,SAAS,GAAG,IAAI;MACpB,IACI,CAAC,IAAI,CAACrC,KAAK,CAACJ,sBAAsB,IAAIgC,eAAe,KACrD,IAAI,CAAC5B,KAAK,CAACO,eAAe,EAC5B;QACE;QACA8B,SAAS,GAAGnF,YAAY,CAACoF,UAAU,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC;QAC7D,IAAIF,SAAS,IAAIG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;UAChDN,iBAAiB,GAAG,IAAI;QAC5B;MACJ;MACA;;MAEAT,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC3B,KAAK,CAACnB,cAAc;MACpD8C,SAAS,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC3B,KAAK,CAAChB,sBAAsB;MAC1D2C,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC3B,KAAK,CAACb,yBAAyB;MAEhE,MAAMwD,MAAM,GAAG;QACXjB,aAAa;QACbC,SAAS;QACTiB,IAAI,EAAE,IAAI,CAACC,4BAA4B,CAAC,CAAC;QACzCC,WAAW,EAAE,IAAI,CAAC9C,KAAK,CAACR;MAC5B,CAAC;MACD;MACAmD,MAAM,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC1E,KAAK,CAAC8E,UAAU,CAACC,UAAU,CAACC,KAAK;MAChEN,MAAM,CAAC,aAAa,CAAC,IAAAd,WAAA,GAAG,IAAI,CAAC7B,KAAK,cAAA6B,WAAA,wBAAAC,oBAAA,GAAVD,WAAA,CAAYvD,QAAQ,cAAAwD,oBAAA,wBAAAC,qBAAA,GAApBD,oBAAA,CAAsBb,SAAS,cAAAc,qBAAA,uBAA/BA,qBAAA,CAAiCmB,WAAW;MACpEP,MAAM,CAAC,cAAc,CAAC,IAAAX,YAAA,GAAG,IAAI,CAAChC,KAAK,cAAAgC,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAY1D,QAAQ,cAAA2D,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBhB,SAAS,cAAAiB,sBAAA,uBAA/BA,sBAAA,CAAiCiB,YAAY;MACtER,MAAM,CAAC,mBAAmB,CAAC,GAAGP,iBAAiB;MAE/C7H,UAAU,CAAC6I,eAAe,CACtB3F,oBAAoB,GAChB,GAAG,GACH,IAAI,CAACQ,KAAK,CAAC8E,UAAU,CAACM,EAAE,GACxB,GAAG,GACH,IAAI,CAACpF,KAAK,CAACqF,YAAY,EAC3BX,MAAM,EACLY,IAAI,IAAK;QACN;QACA,MAAM;UACFX,IAAI;UACJjB,SAAS;UACTD,aAAa;UACb8B,sBAAsB;UACtBC,2BAA2B;UAC3BC;QACJ,CAAC,GAAGH,IAAI,CAACI,IAAI,CAACA,IAAI;QAClB,IAAID,YAAY,EAAE;UACdjK,OAAO,CAACmK,MAAM,CAAC;YACXC,QAAQ,EAAEH,YAAY,CAACG,QAAQ,IAAI;UACvC,CAAC,CAAC;UACFpK,OAAO,CAACiF,KAAK,CAACgF,YAAY,CAACjK,OAAO,CAAC;QACvC;QACA,IAAI,CAAC0G,QAAQ,CACT;UACIX,sBAAsB,EAAEoD,IAAI;UAC5BlD,uBAAuB,EAAE+D;QAC7B,CAAC,EACD,MAAM;UACF;UACA,IAAI,CAACK,cAAc,CACf;YACIH,IAAI,EAAE,IAAI,CAAC3D,KAAK,CAAC1B;UACrB,CAAC,EACD,MAAM;YACF,IAAI,CAACyF,OAAO,CAACnG,OAAO,CAACoG,cAAc,CAC/BR,sBACJ,CAAC;;YAED;YACA,IACIpB,iBAAiB,IACjBC,SAAS,IACT,IAAI,CAACrC,KAAK,CAACO,eAAe,EAC5B;cACE,IAAI,CAAC0D,0BAA0B,CAC3B5B,SAAS,EACTT,eACJ,CAAC;YACL,CAAC,MAAM;cACH,IAAI,CAACO,8BAA8B,CAAC,KAAK,CAAC;cAC1C,IACI,CAAC,IAAI,CAACnC,KAAK,CAACJ,sBAAsB,IAClC,CAACgC,eAAe,IAChB,IAAI,CAAC5B,KAAK,CAACO,eAAe,EAC5B;gBACErD,YAAY,CAACgH,WAAW,CACpB,IAAI,CAAC3B,WAAW,CAAC,CACrB,CAAC;cACL,CAAC,MAAM;gBACH,IAAI,CAACpC,QAAQ,CAAC;kBACVP,sBAAsB,EAAE;gBAC5B,CAAC,CAAC;cACN;YACJ;UACJ,CAAC,EACD,IACJ,CAAC;QACL,CACJ,CAAC;MACL,CAAC,EACAlB,KAAK,IAAK;QACP2B,OAAO,CAAC8D,GAAG,CACP,qCAAqC,EACrC5J,UAAU,CAAC6J,oBAAoB,CAAC1F,KAAK,CACzC,CAAC;QACDjF,OAAO,CAACiF,KAAK,CAAC,+CAA+C,CAAC;QAC9D,IAAI,CAACyD,8BAA8B,CAAC,KAAK,CAAC;MAC9C,CACJ,CAAC;IACL,CAAC;IAAA,KAoBDkC,kBAAkB,GAAG,CAAC3C,aAAa,EAAEC,SAAS,KAAK;MAC/C,IAAIjE,SAAS,EAAE;QACX4G,YAAY,CAAC5G,SAAS,CAAC;MAC3B;MACAA,SAAS,GAAG6G,UAAU,CAAC,MAAM;QACzB,IAAI,CAAC9C,uBAAuB,CAACC,aAAa,EAAEC,SAAS,CAAC;MAC1D,CAAC,EAAE,GAAG,CAAC;IACX,CAAC;IAAA,KACD6C,mBAAmB,GAAIC,OAAO,IAAK;MAC/B,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;QAAA,IAAAC,aAAA,EAAAC,qBAAA;QACtB,IAAI,CAACP,kBAAkB,CACnB;UAAE,CAACI,OAAO,GAAG;QAAK,CAAC,GAAAE,aAAA,GACnB,IAAI,CAACZ,OAAO,cAAAY,aAAA,wBAAAC,qBAAA,GAAZD,aAAA,CAAc/G,OAAO,cAAAgH,qBAAA,uBAArBA,qBAAA,CAAuBC,cAAc,CAAC,CAC1C,CAAC;MACL,CAAC,MAAM;QACHxE,OAAO,CAAC8D,GAAG,CAAC,oBAAoB,CAAC;MACrC;IACJ,CAAC;IAAA,KAEDW,UAAU,GAAInB,IAAI,IAAK;MAAA,IAAAoB,cAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACnB,IAAI,CAACpF,QAAQ,CAAC;QACV9B,gBAAgB,EAAE;MACtB,CAAC,CAAC;MACF,IAAImH,aAAa,GAAG/J,2BAA2B,CAC3CkI,IAAI,GAAAoB,cAAA,GACJ,IAAI,CAAChB,OAAO,cAAAgB,cAAA,uBAAZA,cAAA,CAAcnH,OAAO,EACrB,IAAI,CAAC6H,yBAAyB,CAAC,CAAC,CAACC,MACrC,CAAC;MACDF,aAAa,GAAG;QACZ,GAAGA,aAAa;QAChB,GAAG,IAAI,CAACxE,yBAAyB,CAC7B2C,IAAI,EACJ,IAAI,CAAC8B,yBAAyB,CAAC,CAAC,CAACC,MAAM,GAAAV,qBAAA,GACvC,IAAI,CAAChF,KAAK,CAAC1B,QAAQ,cAAA0G,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqB/D,SAAS,cAAAgE,qBAAA,uBAA9BA,qBAAA,CAAgChE,SACpC;MACJ,CAAC;MACD,IAAI,IAAI,CAACjB,KAAK,CAACvB,QAAQ,EAAE;QAAA,IAAAkH,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;QACrB;QACA,IAAIC,gBAAgB,GAAG1K,iBAAiB,CACpC,IAAI,CAACsE,KAAK,CAACnB,cAAc,GAAA8G,qBAAA,GACzB,IAAI,CAAC3F,KAAK,CAAC1B,QAAQ,cAAAqH,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqB1E,SAAS,cAAA2E,qBAAA,wBAAAC,qBAAA,GAA9BD,qBAAA,CAAgC3E,SAAS,cAAA4E,qBAAA,uBAAzCA,qBAAA,CAA2CQ,WAC/C,CAAC;QACD,IAAID,gBAAgB,EAAE;UAClBZ,aAAa,CAAC,aAAa,CAAC,GAAG,IAAI,CAACxF,KAAK,CAACnB,cAAc;QAC5D;;QAEA;QACA,IAAIyH,mBAAmB,GAAG5K,iBAAiB,CACvC,IAAI,CAACsE,KAAK,CAAChB,sBAAsB,GAAA8G,qBAAA,GACjC,IAAI,CAAC9F,KAAK,CAAC1B,QAAQ,cAAAwH,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqB7E,SAAS,cAAA8E,qBAAA,wBAAAC,qBAAA,GAA9BD,qBAAA,CAAgC9E,SAAS,cAAA+E,qBAAA,uBAAzCA,qBAAA,CAA2CO,SAC/C,CAAC;QACD,IAAID,mBAAmB,EAAE;UACrBd,aAAa,CAAC,WAAW,CAAC,GAAG,IAAI,CAACxF,KAAK,CAAChB,sBAAsB;QAClE;;QAEA;QACA,IAAIwH,sBAAsB,GAAG9K,iBAAiB,CAC1C,IAAI,CAACsE,KAAK,CAACb,yBAAyB,GAAA8G,qBAAA,GACpC,IAAI,CAACjG,KAAK,CAAC1B,QAAQ,cAAA2H,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqBhF,SAAS,cAAAiF,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCjF,SAAS,cAAAkF,sBAAA,uBAAzCA,sBAAA,CAA2CM,YAC/C,CAAC;QACD,IAAID,sBAAsB,EAAE;UACxBhB,aAAa,CAAC,cAAc,CAAC,GACzB,IAAI,CAACxF,KAAK,CAACb,yBAAyB;QAC5C;QACAqG,aAAa,CAAC,yBAAyB,CAAC,GAAG,IAAI,CAACxF,KAAK,CAACF,SAAS;MACnE,CAAC,MAAM;QACH6D,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC3D,KAAK,CAACnB,cAAc;QAC/C8E,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC3D,KAAK,CAAChB,sBAAsB;QACrD2E,IAAI,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC3D,KAAK,CAACb,yBAAyB;QAC3DwE,IAAI,CAAC,yBAAyB,CAAC,GAAG,IAAI,CAAC3D,KAAK,CAACF,SAAS;MAC1D;MACA,IAAI,IAAI,CAACE,KAAK,CAACO,eAAe,EAAE;QAC5B,IAAI8B,SAAS,GAAGnF,YAAY,CAACoF,UAAU,CACnC,IAAI,CAACC,WAAW,CAAC,CAAC,EAClB,IAAI,CAACmC,aAAa,CAAC,CACvB,CAAC;QACD,IAAIrC,SAAS,EAAE;UACXmD,aAAa,GAAGvI,CAAC,CAACyJ,KAAK,CAAClB,aAAa,EAAEnD,SAAS,CAAC;QACrD;MACJ;MAEA,IAAIM,MAAM,GAAG,IAAI,CAAC3C,KAAK,CAACvB,QAAQ,GAAG+G,aAAa,GAAG7B,IAAI;MACvD,IAAI,IAAI,CAACe,aAAa,CAAC,CAAC,EAAE;QACtBf,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC3D,KAAK,CAACnB,cAAc;QAC/C8E,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC3D,KAAK,CAAChB,sBAAsB;QACrD2E,IAAI,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC3D,KAAK,CAACb,yBAAyB;QAC3DwD,MAAM,GAAGgB,IAAI;MACjB;MACA,IAAInB,MAAM,CAACC,IAAI,CAACE,MAAM,CAAC,CAACD,MAAM,IAAI,CAAC,EAAE;QAAA,IAAAiE,sBAAA;QACjC;QACA,MAAMC,8BAA8B,GAChCzJ,kBAAkB,CAAC0J,gCAAgC,EAAAF,sBAAA,GAC/C,IAAI,CAAC3G,KAAK,CAAC1B,QAAQ,cAAAqI,sBAAA,uBAAnBA,sBAAA,CAAqBG,iBAAiB,CAAC,CAAC,CAAC,EACzC,IAAI,CAAC7I,KAAK,CAACqF,YACf,CAAC;QACL,IAAI,CAACsD,8BAA8B,EAAE;UACjC,IAAI,CAACzG,QAAQ,CACT;YACI9B,gBAAgB,EAAE;UACtB,CAAC,EACD,MAAM;YACF5E,OAAO,CAACsN,IAAI,CAAC,mBAAmB,CAAC;UACrC,CACJ,CAAC;UACD;UACA;QACJ;MACJ;MACApE,MAAM,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC1E,KAAK,CAAC8E,UAAU,CAACC,UAAU,CAACC,KAAK;MAChEN,MAAM,CAAC,aAAa,CAAC,IAAAuC,YAAA,GAAG,IAAI,CAAClF,KAAK,cAAAkF,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAY5G,QAAQ,cAAA6G,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBlE,SAAS,cAAAmE,sBAAA,uBAA/BA,sBAAA,CAAiClC,WAAW;MACpEP,MAAM,CAAC,cAAc,CAAC,IAAA0C,YAAA,GAAG,IAAI,CAACrF,KAAK,cAAAqF,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAY/G,QAAQ,cAAAgH,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBrE,SAAS,cAAAsE,sBAAA,uBAA/BA,sBAAA,CAAiCpC,YAAY;MACtE,IAAI,IAAI,CAAC6D,qBAAqB,CAAC,CAAC,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAC9BvE,MAAM,CAAC,2BAA2B,CAAC,GAAG,IAAI,CAAC3C,KAAK,CAACV,YAAY;QAC7DqD,MAAM,CAAC,wBAAwB,CAAC,IAAAsE,qBAAA,GAC5B,IAAI,CAACjH,KAAK,CAACV,YAAY,cAAA2H,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAAyBE,OAAO,cAAAD,sBAAA,uBAAhCA,sBAAA,CAAkCE,IAAI;QAC1CzE,MAAM,CAAC,0BAA0B,CAAC,GAC9B/F,iCAAiC;MACzC;MACA,MAAMyK,UAAU,GAAI9D,IAAI,IAAK;QACzB,IAAI,IAAI,CAACvD,KAAK,CAACO,eAAe,EAAE;UAC5BrD,YAAY,CAACoK,UAAU,CAAC,IAAI,CAAC/E,WAAW,CAAC,CAAC,CAAC;QAC/C;QACA,IAAI,CAACpC,QAAQ,CAAC;UACV9B,gBAAgB,EAAE,KAAK;UACvBK,KAAK,EAAE,EAAE;UACTN,OAAO,EAAE;QACb,CAAC,CAAC;QACF,IAAI,CAACmJ,uBAAuB,CAAChE,IAAI,CAACiE,QAAQ,CAAC;QAC3C,IAAI,CAAC/G,qBAAqB,CAAC,CAAC;QAC5B3E,gCAAgC,CAAC,CAAC;MACtC,CAAC;MACD,MAAM2L,OAAO,GAAI/I,KAAK,IAAK;QACvB;QACA,IAAI,CAACyB,QAAQ,CAAC;UACV9B,gBAAgB,EAAE,KAAK;UACvBK,KAAK,EAAEnE,UAAU,CAAC6J,oBAAoB,CAAC1F,KAAK;QAChD,CAAC,CAAC;MACN,CAAC;MACDnE,UAAU,CAACmN,cAAc,CACrBnK,SAAS,GACL,GAAG,GACH,IAAI,CAACU,KAAK,CAAC8E,UAAU,CAACM,EAAE,GACxB,GAAG,GACH,IAAI,CAACpF,KAAK,CAACqF,YAAY,EAC3BX,MAAM,EACN0E,UAAU,EACVI,OACJ,CAAC;IACL,CAAC;IAAA,KAkDDE,iBAAiB,GAAIC,YAAY,IAAK;MAClC,IAAI,CAACzH,QAAQ,CAAC;QAAEL,SAAS,EAAE8H;MAAa,CAAC,CAAC;;MAE1C;MACA;MACA;MACA;MACA;MACA;IACJ,CAAC;IAAA,KAwZDrF,WAAW,GAAG,MAAM;MAAA,IAAAsF,WAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,cAAA,EAAAC,qBAAA;MAChB,OAAO;QACHC,QAAQ,GAAAL,WAAA,GAAE,IAAI,CAAC5J,KAAK,cAAA4J,WAAA,wBAAAC,qBAAA,GAAVD,WAAA,CAAY9E,UAAU,cAAA+E,qBAAA,uBAAtBA,qBAAA,CAAwBzE,EAAE;QACpC8E,cAAc,GAAAJ,YAAA,GAAE,IAAI,CAAC9J,KAAK,cAAA8J,YAAA,uBAAVA,YAAA,CAAYzE,YAAY;QACxCtD,KAAK,EAAE;UACHnB,cAAc,EAAE,IAAI,CAACmB,KAAK,CAACnB,cAAc;UACzCG,sBAAsB,EAAE,IAAI,CAACgB,KAAK,CAAChB,sBAAsB;UACzDG,yBAAyB,EAAE,IAAI,CAACa,KAAK,CAACb;QAC1C,CAAC;QACDiJ,WAAW,GAAAJ,cAAA,GAAE,IAAI,CAACjE,OAAO,cAAAiE,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAcpK,OAAO,cAAAqK,qBAAA,uBAArBA,qBAAA,CAAuBpD,cAAc,CAAC,CAAC;QACpDH,aAAa,EAAE,IAAI,CAACA,aAAa,CAAC;MACtC,CAAC;IACL,CAAC;IAAA,KAED2D,4BAA4B,GAAG,CAACC,KAAK,EAAEC,aAAa,KAAK;MACrD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;;MAEpD,KAAK,MAAM/G,GAAG,IAAIiH,QAAQ,CAACnC,WAAW,EAAE;QACpC,IAAImC,QAAQ,CAACnC,WAAW,CAACuC,cAAc,CAACrH,GAAG,CAAC,EAAE;UAC1CiH,QAAQ,CAACnC,WAAW,CAAC9E,GAAG,CAAC,GAAGiH,QAAQ,CAACnC,WAAW,CAAC9E,GAAG,CAAC,CAACsH,MAAM,CACvDC,IAAI,IAAK;YAAA,IAAAC,kBAAA;YACN,OAAO,GAAAA,kBAAA,GAACR,aAAa,CAAChH,GAAG,CAAC,cAAAwH,kBAAA,uBAAlBA,kBAAA,CAAoBC,QAAQ,CAACF,IAAI,CAAC;UAC9C,CACJ,CAAC;QACL;MACJ;MACA,OAAON,QAAQ;IACnB,CAAC;IA3wCG,IAAI,CAACzE,OAAO,GAAG/K,KAAK,CAACiQ,SAAS,CAAC,CAAC;IAChC,IAAI,CAACnI,SAAS,GAAG9H,KAAK,CAACiQ,SAAS,CAAC,CAAC;IAClC;EACJ;EAwCAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,IAAI,CAAClJ,mBAAmB,CAAC,CAAC;EAC9B;EA2BAmJ,iBAAiBA,CAAA,EAAG;IAChB;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA,IAAI,IAAI,CAACpC,qBAAqB,CAAC,CAAC,EAAE;MAC9B,IAAI,CAAC7G,QAAQ,CAAC;QACVd,iBAAiB,EAAE;MACvB,CAAC,CAAC;MACF,IAAI,CAACgK,uBAAuB,CAAC,CAAC;MAC9B;IACJ;EACJ;EAEArC,qBAAqBA,CAAA,EAAG;IAAA,IAAAsC,qBAAA,EAAAC,sBAAA;IACpB,MAAMC,iBAAiB,GAAG,IAAI,CAACvL,KAAK,CAACqF,YAAY;IACjD,MAAMmG,iCAAiC,IAAAH,qBAAA,GACnC,IAAI,CAACrL,KAAK,CAACyL,iBAAiB,cAAAJ,qBAAA,wBAAAC,sBAAA,GAA5BD,qBAAA,CAA8BK,WAAW,cAAAJ,sBAAA,uBAAzCA,sBAAA,CACMK,2BAA2B;IACrC,OACIlN,OAAO,CAAC+M,iCAAiC,CAAC,IAC1CA,iCAAiC,CAACT,QAAQ,CAACQ,iBAAiB,CAAC;IAEjE;EACJ;EAEAH,uBAAuBA,CAAA,EAAG;IACtB,OAAO,IAAIQ,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MAAA,IAAAC,eAAA;MACpC3J,OAAO,CAAC8D,GAAG,CAAC,aAAa,EAAE,wBAAwB,CAAC;MACpD,KAAA6F,eAAA,GAAIC,MAAM,CAACC,OAAO,cAAAF,eAAA,uBAAdA,eAAA,CAAgBG,cAAc,EAAE;QAAA,IAAAC,gBAAA;QAChC,MAAMC,wBAAwB,GAC1B,mBAAmB,GAAGzO,YAAY,CAAC,CAAC;QACxCqO,MAAM,CAACK,gBAAgB,CAACD,wBAAwB,EAAGE,CAAC,IAAK;UACrD,MAAMjL,YAAY,GAAGiL,CAAC,CAACC,MAAM;UAC7BjG,UAAU,CAAC,MAAM;YACb,IAAI,CAACkG,mBAAmB,CAACnL,YAAY,CAAC;UAC1C,CAAC,EAAE,IAAI,CAAC;;UAER;QACJ,CAAC,CAAC;QACF,MAAMoL,WAAW,IAAAN,gBAAA,GAAGH,MAAM,CAACC,OAAO,cAAAE,gBAAA,uBAAdA,gBAAA,CAAgBD,cAAc,CAC9CE,wBACJ,CAAC;QACDP,OAAO,CAAC,CAAC;MACb,CAAC,MAAM;QACH;QACA;QACArQ,OAAO,CAACkR,OAAO,CAAC,wBAAwB,CAAC;QACzC;QACA;QACA;QACAb,OAAO,CAAC,CAAC;MACb;IACJ,CAAC,CAAC;EACN;EAEAW,mBAAmBA,CAACnL,YAAY,EAAE;IAC9B,IAAI,CAACa,QAAQ,CAAC;MACVd,iBAAiB,EAAE,KAAK;MACxBC,YAAY,EAAEA;IAClB,CAAC,CAAC;EACN;EAEA6J,YAAYA,CAAA,EAAG;IACX;IACA,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB,IACK,IAAI,CAACpJ,KAAK,CAACvB,QAAQ,IAAI,IAAI,CAACuB,KAAK,CAAC5B,OAAO,IACzC,CAAC,IAAI,CAAC4B,KAAK,CAACvB,QAAQ,IACjB,IAAI,CAACuB,KAAK,CAAC1B,QAAQ,IAAIC,SAAS,IAChC,CAAC,IAAI,CAACyB,KAAK,CAACxB,iBAAkB,EACpC;MAAA,IAAAoM,sBAAA;MACE,IAAI,CAACzK,QAAQ,CAAC;QACV3B,iBAAiB,EAAE;MACvB,CAAC,CAAC;MACF,IAAImE,MAAM,GAAG,CAAC,CAAC;MACfA,MAAM,CAAC,eAAe,CAAC,GACnB,EAAE,GAAG,IAAI,CAAC1E,KAAK,CAAC8E,UAAU,CAACC,UAAU,CAACC,KAAK;MAC/CN,MAAM,CAAC,WAAW,CAAC,IAAAiI,sBAAA,GAAG,IAAI,CAAC3M,KAAK,CAAC8E,UAAU,CAAC8H,gBAAgB,cAAAD,sBAAA,uBAAtCA,sBAAA,CAAwCvH,EAAE;MAChE,MAAMgE,UAAU,GAAI9D,IAAI,IAAK;QACzB,IAAI,CAACO,cAAc,CACfP,IAAI;QACJ;QACA,IAAI,CAACpD,QAAQ,CACT;UACI3B,iBAAiB,EAAE,KAAK;UACxBF,QAAQ,EAAEiF,IAAI,CAACI,IAAI;UACnBjF,KAAK,EAAE;QACX,CAAC,EACD,MAAM;UACF,IAAI,IAAI,CAACgG,aAAa,CAAC,CAAC,EAAE;YACtB,IAAI,CAACL,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC;QACJ,CACJ,CACJ,CAAC;MACL,CAAC;MACD,MAAMoD,OAAO,GAAI/I,KAAK,IAAK;QACvB;QACA,IAAI,CAACyB,QAAQ,CAAC;UACV3B,iBAAiB,EAAE,KAAK;UACxBE,KAAK,EAAEnE,UAAU,CAAC6J,oBAAoB,CAAC1F,KAAK;QAChD,CAAC,CAAC;MACN,CAAC;MACD,IAAIoM,GAAG,GACHxN,QAAQ,GACR,GAAG,GACH,IAAI,CAACW,KAAK,CAAC8E,UAAU,CAACM,EAAE,GACxB,GAAG,GACH,IAAI,CAACpF,KAAK,CAACqF,YAAY;MAC3B;MACA/I,UAAU,CAACwQ,cAAc,CAACD,GAAG,EAAEnI,MAAM,EAAE0E,UAAU,EAAEI,OAAO,CAAC;IAC/D;EACJ;EAEA3D,cAAcA,CAACkH,mBAAmB,EAAEC,IAAI,EAAEC,mBAAmB,GAAG,KAAK,EAAE;IAAA,IAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACnE,IAAIC,6BAA6B,GAC7BvO,uBAAuB,CAACwO,iCAAiC,EAAAL,sBAAA,GACrD,IAAI,CAAClN,KAAK,CAACyL,iBAAiB,cAAAyB,sBAAA,uBAA5BA,sBAAA,CAA8BxB,WAAW,EACzC,IAAI,CAAC1L,KAAK,CAACqF,YACf,CAAC;IACL,IAAImI,eAAe,GAAG,CAClBjQ,qBAAqB,CAAC+P,6BAA6B,CAAC,CACvD;IACD,IAAIG,oBAAoB,GAAG,IAAI,CAACC,8BAA8B,CAAC,CAAC;IAChE,IAAID,oBAAoB,IAAIA,oBAAoB,CAAChJ,MAAM,GAAG,CAAC,EAAE;MAAA,IAAAkJ,sBAAA,EAAAC,sBAAA;MACzD,MAAMC,uBAAuB,IAAAF,sBAAA,GACzB,IAAI,CAAC3N,KAAK,CAACyL,iBAAiB,cAAAkC,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BjC,WAAW,cAAAkC,sBAAA,uBAAzCA,sBAAA,CACI,wCAAwC,IAAI,CAAC5N,KAAK,CAACqF,YAAY,EAAE,CACpE;MACLmI,eAAe,GAAGK,uBAAuB,GACnC,CAAC,GAAGJ,oBAAoB,EAAE,GAAGD,eAAe,CAAC,GAC7C,CAAC,GAAGA,eAAe,EAAE,GAAGC,oBAAoB,CAAC;IACvD;IACA;;IAEA;IACA,IAAIK,qBAAqB,GAAGb,mBAAmB,GACzC,IAAI,CAAClL,KAAK,CAACnB,cAAc,GACzB,CAAC,CAAC;IAER,KAAAuM,qBAAA,GAAIJ,mBAAmB,CAACrH,IAAI,CAAC1C,SAAS,cAAAmK,qBAAA,uBAAlCA,qBAAA,CAAoCnK,SAAS,EAAE;MAAA,IAAA+K,sBAAA;MAC/CD,qBAAqB,IAAAC,sBAAA,GACjBhB,mBAAmB,CAACrH,IAAI,CAAC1C,SAAS,cAAA+K,sBAAA,uBAAlCA,sBAAA,CAAoC/K,SAAS,CAAC,aAAa,CAAC;IACpE;IAEA,IAAIgL,cAAc,GAAG,EAAE;IACvB,IAAIC,iBAAiB,GAAG,IAAI,CAACC,6BAA6B,CAAC,CAAC;IAC5D,IAAID,iBAAiB,IAAIA,iBAAiB,CAACxJ,MAAM,GAAG,CAAC,EAAE;MACnDuJ,cAAc,GAAG,CAAC,GAAGA,cAAc,EAAE,GAAGC,iBAAiB,CAAC;IAC9D;IAEA,IAAIE,6BAA6B,GAAGlB,mBAAmB,GACjD,IAAI,CAAClL,KAAK,CAAChB,sBAAsB,GACjC,CAAC,CAAC;IAER,KAAAqM,sBAAA,GAAIL,mBAAmB,CAACrH,IAAI,CAAC1C,SAAS,cAAAoK,sBAAA,uBAAlCA,sBAAA,CAAoCpK,SAAS,EAAE;MAAA,IAAAoL,sBAAA;MAC/CD,6BAA6B,IAAAC,sBAAA,GACzBrB,mBAAmB,CAACrH,IAAI,CAAC1C,SAAS,cAAAoL,sBAAA,uBAAlCA,sBAAA,CAAoCpL,SAAS,CAAC,WAAW,CAAC;IAClE;IAEA,IAAIqL,iBAAiB,GAAG,EAAE;IAC1B,IAAIC,oBAAoB,GAAG,IAAI,CAACC,gCAAgC,CAAC,CAAC;IAClE,IAAID,oBAAoB,IAAIA,oBAAoB,CAAC7J,MAAM,GAAG,CAAC,EAAE;MACzD4J,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB,EAAE,GAAGC,oBAAoB,CAAC;IACvE;IAEA,IAAIE,gCAAgC,GAAGvB,mBAAmB,GACpD,IAAI,CAAClL,KAAK,CAACb,yBAAyB,GACpC,CAAC,CAAC;IACR,KAAAmM,sBAAA,GAAIN,mBAAmB,CAACrH,IAAI,CAAC1C,SAAS,cAAAqK,sBAAA,uBAAlCA,sBAAA,CAAoCrK,SAAS,EAAE;MAAA,IAAAyL,sBAAA;MAC/CD,gCAAgC,IAAAC,sBAAA,GAC5B1B,mBAAmB,CAACrH,IAAI,CAAC1C,SAAS,cAAAyL,sBAAA,uBAAlCA,sBAAA,CAAoCzL,SAAS,CAAC,cAAc,CAAC;IACrE;IAEA,IAAI,CAACd,QAAQ,CACT;MACIvB,YAAY,EAAE6M,eAAe;MAC7B5M,cAAc,EAAE;QAAE,GAAGkN;MAAsB,CAAC;MAC5ChN,WAAW,EAAEkN,cAAc;MAC3BjN,sBAAsB,EAAE;QAAE,GAAGoN;MAA8B,CAAC;MAC5DlN,cAAc,EAAEoN,iBAAiB;MACjCnN,yBAAyB,EAAE;QACvB,GAAGsN;MACP;IACJ,CAAC,EACDxB,IACJ,CAAC;EACL;EAEAkB,6BAA6BA,CAAA,EAAG;IAC5B,OAAO7P,wBAAwB,CAAC,IAAI,CAACqQ,4BAA4B,CAAC,CAAC,CAAC;EACxE;EAEAH,gCAAgCA,CAAA,EAAG;IAC/B,OAAOjQ,2BAA2B,CAAC,IAAI,CAACoQ,4BAA4B,CAAC,CAAC,CAAC;EAC3E;EAEAhB,8BAA8BA,CAAA,EAAG;IAC7B,OAAOtP,yBAAyB,CAAC,IAAI,CAACsQ,4BAA4B,CAAC,CAAC,CAAC;EACzE;EAEAC,kBAAkBA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACrC,IACID,SAAS,CAAC9J,UAAU,IAAI,IAAI,CAAC9E,KAAK,CAAC8E,UAAU,IAC7C8J,SAAS,CAACE,UAAU,IAAI,IAAI,CAAC9O,KAAK,CAAC8O,UAAU,EAC/C;MACE,IAAI,CAAC5M,QAAQ,CACT;QACIhC,aAAa,EAAE,CAAC,IAAI,CAAC6B,KAAK,CAAC7B,aAAa;QACxCC,OAAO,EAAE,IAAI,CAACH,KAAK,CAAC8O,UAAU;QAC9BhN,eAAe,EAAE,CAAC,CAAE;MACxB,CAAC,EACD,YAAY;QACR,IAAI,IAAI,CAAC9B,KAAK,CAAC8O,UAAU,IAAI,IAAI,CAAC/M,KAAK,CAACvB,QAAQ,EAAE;UAC9C,IAAI,CAAC0K,YAAY,CAAC,CAAC;QACvB;MACJ,CACJ,CAAC;IACL,CAAC,MAAM;MACH,IAAI,IAAI,CAACnJ,KAAK,CAACgN,eAAe,EAAE;QAC5B,IAAI,CAAC7M,QAAQ,CACT;UACI6M,eAAe,EAAE;QACrB,CAAC,EACD,IAAI,CAAC7D,YAAY,CAAC,CACtB,CAAC;MACL;IACJ;EACJ;EAUA1I,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACxC,KAAK,CAACgP,OAAO,IAAI1O,SAAS,EAAE;MACjC,IAAI,CAACN,KAAK,CAACgP,OAAO,CAAC,CAAC;IACxB;IACA,IAAI,CAAC9M,QAAQ,CAAC;MACV6M,eAAe,EAAE,IAAI;MACrB,GAAG,IAAI,CAAC9O;IACZ,CAAC,CAAC;EACN;EACA2C,wBAAwBA,CAAA,EAAG;IACvB2B,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzC,KAAK,CAACZ,8BAA8B,CAAC,CAACiC,OAAO,CACzD6L,SAAS,IACL,IAAI,CAAClN,KAAK,CAACZ,8BAA8B,CAAC8N,SAAS,CAAC,GAAG,IAChE,CAAC;EACL;EAEA3F,uBAAuBA,CAACC,QAAQ,EAAE;IAC9B;IACA,IAAI,IAAI,CAACvJ,KAAK,CAACkP,cAAc,IAAI5O,SAAS,EAAE;MACxC,IAAI,CAACN,KAAK,CAACkP,cAAc,CAAC3F,QAAQ,CAAC;IACvC;EACJ;EAEA4F,mBAAmBA,CAAA,EAAG;IAClB3T,OAAO,CAACsN,IAAI,CACR;MACIsG,KAAK,EAAE;QACHC,SAAS,EAAE;MACf,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,EACD,IACJ,CAAC;EACL;EAuBAC,0BAA0BA,CAACC,cAAc,EAAE;IACvC,IAAIC,mBAAmB,GAAG,EAAE;IAC5B,IAAI,CAACzP,KAAK,CAACyL,iBAAiB,CAACiE,QAAQ,CAACtM,OAAO,CAAEuM,YAAY,IAAK;MAC5D,IAAIH,cAAc,CAACzE,QAAQ,CAAC4E,YAAY,CAAC3K,KAAK,CAAC,EAAE;QAC7CyK,mBAAmB,CAACG,IAAI,CAACD,YAAY,CAACE,KAAK,CAAC;MAChD;IACJ,CAAC,CAAC;IACF,OAAOJ,mBAAmB,CAACK,IAAI,CAAC,IAAI,CAAC;EACzC;EAgBAlL,4BAA4BA,CAAA,EAAG;IAAA,IAAAmL,sBAAA;IAC3B,IAAIC,aAAa,GAAGxR,2BAA2B,CAC3C,IAAI,CAACwB,KAAK,CAACqF,YAAY,GAAA0K,sBAAA,GACvB,IAAI,CAAC/P,KAAK,CAACyL,iBAAiB,cAAAsE,sBAAA,uBAA5BA,sBAAA,CAA8BrE,WAClC,CAAC;IACD,IAAIsE,aAAa,EAAE;MAAA,IAAAC,WAAA;MACfD,aAAa,IAAAC,WAAA,GAAGzF,IAAI,CAACC,KAAK,CAACuF,aAAa,CAAC,cAAAC,WAAA,uBAAzBA,WAAA,CAA2BC,gBAAgB;MAC3D;IACJ;IACA,OAAOF,aAAa;EACxB;EAEA9L,8BAA8BA,CAACc,KAAK,EAAE;IAClC,IAAI,CAAC9C,QAAQ,CAAC;MACVV,2BAA2B,EAAEwD;IACjC,CAAC,CAAC;EACN;;EAEA;EACAmL,qBAAqBA,CAAC/L,SAAS,EAAEgM,iBAAiB,EAAE;IAChD,IAAI,CAAChM,SAAS,IAAI,CAACgM,iBAAiB,EAAE,OAAO,CAAC,CAAC;IAE/C,MAAMC,aAAa,GAAG,CAAC,CAAC;IACxB9L,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAAChB,OAAO,CAAEE,GAAG,IAAK;MACpC;MACA,IAAI,CAAC,aAAa,EAAE,WAAW,EAAE,cAAc,CAAC,CAACyH,QAAQ,CAACzH,GAAG,CAAC,EAAE;QAC5D;MACJ;MAEA,MAAMgN,UAAU,GAAGlM,SAAS,CAACd,GAAG,CAAC;MACjC,MAAMiN,YAAY,GAAGH,iBAAiB,CAAC9M,GAAG,CAAC;;MAE3C;MACA,IAAIgN,UAAU,IAAI,CAACC,YAAY,EAAE;QAC7BF,aAAa,CAAC/M,GAAG,CAAC,GAAGgN,UAAU;MACnC;IACJ,CAAC,CAAC;IAEF,OAAOD,aAAa;EACxB;;EAEA;EACArK,0BAA0BA,CAAC5B,SAAS,EAAET,eAAe,GAAG,KAAK,EAAE;IAC3D;IACA,IAAI,CAAC,IAAI,CAAC5B,KAAK,CAACO,eAAe,EAAE;MAC7B,IAAI,CAACkO,wBAAwB,CAAC,CAAC;MAC/B;IACJ;IAEA,MAAMC,kBAAkB,GAAG,CAAC,CAAC,CAAC;IAC9B,MAAML,iBAAiB,GAAG,IAAI,CAACtK,OAAO,CAACnG,OAAO,CAACiH,cAAc,CAAC,CAAC;IAC/DxE,OAAO,CAAC8D,GAAG,CAAC,mBAAmB,EAAEkK,iBAAiB,CAAC;IACnD,MAAMC,aAAa,GAAG,IAAI,CAACF,qBAAqB,CAC5C/L,SAAS,EACTgM,iBACJ,CAAC;IACDhO,OAAO,CAAC8D,GAAG,CAAC,eAAe,EAAEmK,aAAa,CAAC;IAE3C,IACI9L,MAAM,CAACC,IAAI,CAAC6L,aAAa,CAAC,CAAC5L,MAAM,GAAG,CAAC,IACrC,IAAI,CAAC1C,KAAK,CAACD,eAAe,GAAG2O,kBAAkB,EACjD;MACE;MACA,IAAI,CAAC3K,OAAO,CAACnG,OAAO,CAACoG,cAAc,CAACsK,aAAa,CAAC;;MAElD;MACA,MAAMK,iBAAiB,GAAG,IAAI,CAAC5K,OAAO,CAACnG,OAAO,CAACiH,cAAc,CAAC,CAAC;MAE/DxE,OAAO,CAAC8D,GAAG,CAAC,mBAAmB,EAAEwK,iBAAiB,CAAC;;MAEnD;MACA,MAAMC,kBAAkB,GAAG,IAAI,CAACR,qBAAqB,CACjD/L,SAAS,EACTsM,iBACJ,CAAC;MAEDtO,OAAO,CAAC8D,GAAG,CAAC,oBAAoB,EAAEyK,kBAAkB,CAAC;MAErD,IAAIpM,MAAM,CAACC,IAAI,CAACmM,kBAAkB,CAAC,CAAClM,MAAM,GAAG,CAAC,EAAE;QAC5C;QACArC,OAAO,CAAC8D,GAAG,CACP,8DAA8D,EAC9DyK,kBAAkB,EAClB,UAAU,EACV,IAAI,CAAC5O,KAAK,CAACD,eAAe,GAAG,CACjC,CAAC;QACD,IAAI,CAACI,QAAQ,CAAC;UACVJ,eAAe,EAAE,IAAI,CAACC,KAAK,CAACD,eAAe,GAAG;QAClD,CAAC,CAAC;QACFwE,UAAU,CAAC,MAAM;UACb,IAAI,CAAC9C,uBAAuB,CAAC,CAAC,CAAC,EAAEkN,iBAAiB,EAAE,IAAI,CAAC;QAC7D,CAAC,EAAE,GAAG,CAAC;MACX,CAAC,MAAM;QACH;QACA,IAAI,CAACF,wBAAwB,CAAC,CAAC;MACnC;IACJ,CAAC,MAAM;MACH;MACA,IAAI,CAACA,wBAAwB,CAAC,CAAC;IACnC;EACJ;;EAEA;EACAA,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAACtM,8BAA8B,CAAC,KAAK,CAAC;IAC1C,IAAI,CAAChC,QAAQ,CAAC;MACVP,sBAAsB,EAAE,KAAK;MAC7BG,eAAe,EAAE,CAAC,CAAE;IACxB,CAAC,CAAC;EACN;EA0HA8O,qBAAqBA,CAAA,EAAG;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACpB,OAAO;MACHC,aAAa,GAAAb,sBAAA,GAAE,IAAI,CAAC7Q,KAAK,CAAC8E,UAAU,cAAA+L,sBAAA,wBAAAC,sBAAA,GAArBD,sBAAA,CAAuB9L,UAAU,cAAA+L,sBAAA,uBAAjCA,sBAAA,CAAmC9L,KAAK;MACvDC,WAAW,GAAA8L,YAAA,GAAE,IAAI,CAAChP,KAAK,cAAAgP,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAY1Q,QAAQ,cAAA2Q,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBhO,SAAS,cAAAiO,sBAAA,uBAA/BA,sBAAA,CAAiChM,WAAW;MACzDC,YAAY,GAAAgM,YAAA,GAAE,IAAI,CAACnP,KAAK,cAAAmP,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAY7Q,QAAQ,cAAA8Q,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBnO,SAAS,cAAAoO,sBAAA,uBAA/BA,sBAAA,CAAiClM,YAAY;MAC3DqE,QAAQ,GAAA8H,sBAAA,GAAE,IAAI,CAACrR,KAAK,CAAC8E,UAAU,cAAAuM,sBAAA,uBAArBA,sBAAA,CAAuBjM,EAAE;MACnC8E,cAAc,GAAAoH,YAAA,GAAE,IAAI,CAACtR,KAAK,cAAAsR,YAAA,uBAAVA,YAAA,CAAYjM,YAAY;MACxCsM,eAAe,GAAAJ,YAAA,GAAE,IAAI,CAACxP,KAAK,cAAAwP,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAYlR,QAAQ,cAAAmR,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBxO,SAAS,cAAAyO,sBAAA,uBAA/BA,sBAAA,CAAiCE;IACtD,CAAC;EACL;EAEAC,iBAAiBA,CAAA,EAAG;IAChB,OAAO;MACHC,iBAAiB,EAAE,IAAI,CAAC9P,KAAK,CAACL,6BAA6B;MAC3DoQ,kBAAkB,EAAE,IAAI,CAAClB,qBAAqB,CAAC;IACnD,CAAC;EACL;EAwJAmB,aAAaA,CAAA,EAAG;IAAA,IAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACZ,IAAI,CAAChQ,QAAQ,CAAC;MACV9B,gBAAgB,EAAE,IAAI;MACtBkB,aAAa,EAAE,IAAI;MACnB6Q,UAAU,EAAEC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,GAAG;IACzC,CAAC,CAAC;IAEF,IAAI7N,MAAM,GAAG,CAAC,CAAC;IACfA,MAAM,CAAC,WAAW,CAAC,GAAG,IAAI;IAC1BA,MAAM,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC1E,KAAK,CAAC8E,UAAU,CAACM,EAAE;IAChD,MAAMgE,UAAU,GAAI9D,IAAI,IAAK;MACzB,IAAI,CAACpD,QAAQ,CAAC;QACV9B,gBAAgB,EAAE;MACtB,CAAC,CAAC;IACN,CAAC;IACD,MAAMoJ,OAAO,GAAI/I,KAAK,IAAK;MACvB;MACA,IAAI,CAACyB,QAAQ,CAAC;QACV9B,gBAAgB,EAAE,KAAK;QACvBK,KAAK,EAAEnE,UAAU,CAAC6J,oBAAoB,CAAC1F,KAAK;MAChD,CAAC,CAAC;IACN,CAAC;IACDnE,UAAU,CAACwQ,cAAc,CACrBvN,YAAY,GAAG,GAAG,KAAAyS,YAAA,GAAG,IAAI,CAACjQ,KAAK,cAAAiQ,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAY3R,QAAQ,cAAA4R,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBjP,SAAS,cAAAkP,sBAAA,uBAA/BA,sBAAA,CAAiCjN,WAAW,GACjEP,MAAM,EACN0E,UAAU,EACVI,OACJ,CAAC;EACL;EAEAgJ,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACtQ,QAAQ,CAAC;MACVhC,aAAa,EAAE,CAAC,IAAI,CAAC6B,KAAK,CAAC7B;IAC/B,CAAC,CAAC;EACN;EACAuS,oBAAoBA,CAACC,iBAAiB,EAAE;IACpC,OAAO;MACHpP,GAAG,EAAE,SAAS;MACdqP,OAAO,EAAED,iBAAiB;MAC1BE,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,UAAU;MAClBC,KAAK,EAAE,CACH;QACIC,QAAQ,EAAE;MACd,CAAC;IAET,CAAC;EACL;EAWAC,kBAAkBA,CAAA,EAAG;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACjB,MAAMC,kBAAkB,IAAA/B,sBAAA,GACpB,IAAI,CAACnN,OAAO,CAACnG,OAAO,cAAAsT,sBAAA,uBAApBA,sBAAA,CAAsBgC,aAAa,CAAC,kBAAkB,CAAC;IAC3D,MAAMC,UAAU,GACZ,EAAAhC,sBAAA,OAAI,CAACnR,KAAK,CAAC1B,QAAQ,cAAA6S,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBrK,iBAAiB,CAAC,CAAC,CAAC,cAAAsK,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2CzH,WAAW,cAAA0H,sBAAA,uBAAtDA,sBAAA,CACM+B,2BAA2B,KAAI,SAAS;IAClD,MAAMC,QAAQ,GACV,EAAA/B,sBAAA,OAAI,CAACtR,KAAK,CAAC1B,QAAQ,cAAAgT,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBxK,iBAAiB,CAAC,CAAC,CAAC,cAAAyK,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2C5H,WAAW,cAAA6H,sBAAA,uBAAtDA,sBAAA,CACM8B,2BAA2B,KAAI,SAAS;IAElD,IAAIC,YAAY,IAAA9B,YAAA,GACZ,IAAI,CAACzR,KAAK,cAAAyR,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAYnT,QAAQ,cAAAoT,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBzQ,SAAS,cAAA0Q,sBAAA,uBAA/BA,sBAAA,CACM6B,mCAAmC;IAC7C,IAAIC,yBAAyB,IAAA7B,YAAA,GACzB,IAAI,CAAC5R,KAAK,cAAA4R,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAYtT,QAAQ,cAAAuT,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsB5Q,SAAS,cAAA6Q,sBAAA,uBAA/BA,sBAAA,CAAiC4B,gBAAgB;IACrD,IAAIC,+BAA+B,IAAA5B,YAAA,GAC/B,IAAI,CAAC/R,KAAK,cAAA+R,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAYzT,QAAQ,cAAA0T,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsB/Q,SAAS,cAAAgR,sBAAA,uBAA/BA,sBAAA,CACM2B,oCAAoC;IAC9C,IAAIC,oCAAoC,IAAA3B,aAAA,GACpC,IAAI,CAAClS,KAAK,cAAAkS,aAAA,wBAAAC,qBAAA,GAAVD,aAAA,CAAY5T,QAAQ,cAAA6T,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBlR,SAAS,cAAAmR,sBAAA,uBAA/BA,sBAAA,CACM0B,0CAA0C;IACpD,IAAIC,qBAAqB,IAAA1B,aAAA,GACrB,IAAI,CAACrS,KAAK,cAAAqS,aAAA,wBAAAC,qBAAA,GAAVD,aAAA,CAAY/T,QAAQ,cAAAgU,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBrR,SAAS,cAAAsR,sBAAA,uBAA/BA,sBAAA,CAAiCpP,YAAY;IACjD,IAAI6Q,eAAe,GAAG,EAAAxB,YAAA,OAAI,CAACvU,KAAK,cAAAuU,YAAA,wBAAAC,qBAAA,GAAVD,YAAA,CAAYzP,UAAU,cAAA0P,qBAAA,uBAAtBA,qBAAA,CAAwBuB,eAAe,KAAI,KAAK;IACtE,MAAMtK,iBAAiB,GAAG,IAAI,CAACuK,oBAAoB,CAAC,CAAC;IACrD,MAAMC,uBAAuB,GACzB,EAAAxB,sBAAA,OAAI,CAAC1S,KAAK,CAAC1B,QAAQ,cAAAoU,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqB5L,iBAAiB,CAAC,CAAC,CAAC,cAAA6L,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2ChJ,WAAW,cAAAiJ,sBAAA,uBAAtDA,sBAAA,CACMuB,6BAA6B,KAAI,EAAE;IAC7C,IAAIC,yBAAyB,GAAG,KAAK;IACrC,IAAIF,uBAAuB,CAAClL,QAAQ,CAAC,IAAI,CAAC/K,KAAK,CAACqF,YAAY,CAAC,EAAE;MAC3D8Q,yBAAyB,GAAG,IAAI;IACpC;IACA,MAAMC,cAAc,GAAG,CACnB;MACI9S,GAAG,EAAE,iBAAiB;MACtBsP,KAAK,eACD7X,KAAA,CAAAsb,aAAA;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACI5b,KAAA,CAAAsb,aAAA;QAAGO,SAAS,EAAC,4BAA4B;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAI,CAAC,cAC/C,CACN;MACD9D,MAAM,EAAE,aAAa;MACrBE,QAAQ,EAAE,IAAI;MACdJ,OAAO,EAAE,CAAC;MACVkE,WAAW,EAAE;QACTnX,YAAY,EAAEA,YAAY;QAC1B0P,KAAK,EAAE;UACH0H,KAAK,EAAE;QACX,CAAC;QACDC,QAAQ,EAAEA,CAAC/R,KAAK,EAAEgS,UAAU,KAAK;UAC7B,IAAI,CAAClR,OAAO,CAACnG,OAAO,CAACoG,cAAc,CAAC;YAChCkR,eAAe,EAAEhZ,MAAM,CAACiZ,GAAG,CAACF,UAAU;UAC1C,CAAC,CAAC;QACN;MACJ;IACJ,CAAC,EACD;MACI1T,GAAG,EAAE,QAAQ;MACbqP,OAAO,EAAE,CAAC;MACVwE,MAAMA,CAAA,EAAG;QACL,oBAAOpc,KAAA,CAAAsb,aAAA,CAAAtb,KAAA,CAAAqc,QAAA,MAAI,CAAC;MAChB;IACJ,CAAC,EACD;MACI9T,GAAG,EAAE,kBAAkB;MACvBsP,KAAK,eACD7X,KAAA,CAAAsb,aAAA;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACI5b,KAAA,CAAAsb,aAAA;QAAGO,SAAS,EAAC,8BAA8B;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAI,CAAC,cAEjD,CACN;MACD9D,MAAM,EAAE7U,gBAAgB;MACxB+U,QAAQ,EAAE,IAAI;MACd8D,WAAW,EAAE;QACTQ,UAAU,EAAEnC,UAAU;QACtBoC,QAAQ,EAAElC,QAAQ;QAClBmC,IAAI,EAAE,EAAE;QACRR,QAAQ,EAAG/R,KAAK,IAAK;UACjB,IAAI,CAAC9C,QAAQ,CAAC;YACVhC,aAAa,EAAE,CAAC,IAAI,CAAC6B,KAAK,CAAC7B;UAC/B,CAAC,CAAC;QACN;MACJ,CAAC;MACDyS,OAAO,EAAE;IACb,CAAC,EACD;MACIrP,GAAG,EAAE,gBAAgB;MACrBsP,KAAK,eACD7X,KAAA,CAAAsb,aAAA;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACI5b,KAAA,CAAAsb,aAAA;QAAGO,SAAS,EAAC,8BAA8B;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAI,CAAC,YACjD,CACN;MACD9D,MAAM,EAAE7U,gBAAgB;MACxB+U,QAAQ,EAAE,IAAI;MACd8D,WAAW,EAAE;QACTQ,UAAU,EAAErC,kBAAkB,GACxBA,kBAAkB,GAClBE,UAAU;QAChBoC,QAAQ,EAAElC,QAAQ;QAClBmC,IAAI,EAAE;MACV,CAAC;MACD5E,OAAO,EAAE;IACb,CAAC,CACJ;IAED,IAAID,iBAAiB,GACjB,EAAAkC,sBAAA,OAAI,CAAC5U,KAAK,CAACyL,iBAAiB,cAAAmJ,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BlJ,WAAW,cAAAmJ,sBAAA,uBAAzCA,sBAAA,CACI,kBAAkB,IAAI,CAAC7U,KAAK,CAACqF,YAAY,iBAAiB,CAC7D,KAAI,CAAC;IACV,MAAMmS,mBAAmB,IAAA1C,sBAAA,GACrB,IAAI,CAAC9U,KAAK,CAACyL,iBAAiB,cAAAqJ,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BpJ,WAAW,cAAAqJ,sBAAA,uBAAzCA,sBAAA,CACI,oCAAoC,IAAI,CAAC/U,KAAK,CAACqF,YAAY,EAAE,CAChE;IACL,MAAMV,IAAI,GAAG;MACT8S,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBjQ,MAAM,EAAE;MACJ;MACA,IAAI0O,yBAAyB,GAAG,CAAC,IAAI,CAACwB,YAAY,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAC3D,GAAG,IAAI,CAACnQ,yBAAyB,CAAC,CAAC,CAACC,MAAM,EAC1C,IAAI,IAAI,CAACzH,KAAK,CAACqF,YAAY,IAAI,oBAAoB,GAC7C+Q,cAAc,GACd,EAAE,CAAC,EACT,IAAI,CAACoB,mBAAmB,GAClB,CAAC,IAAI,CAAC/E,oBAAoB,CAACC,iBAAiB,CAAC,CAAC,GAC9C,EAAE,CAAC;IAEjB,CAAC;IAED,IACI4C,YAAY,IACZE,yBAAyB,IACzB,CAAA/J,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEzG,KAAK,KAAI0Q,+BAA+B,KAC3DE,oCAAoC,aAApCA,oCAAoC,uBAApCA,oCAAoC,CAAE7K,QAAQ,CAC1C+K,qBACJ,CAAC,GACH;MACEnR,IAAI,CAAC8C,MAAM,CAACmI,IAAI,CACZ;QACItM,GAAG,EAAE,KAAK;QACVqP,OAAO,EAAE,CAAC;QACVC,KAAK,EAAE,SAASmD,eAAe,EAAE;QACjClD,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,CACH;UACI8E,OAAO,EAAE,IAAIC,MAAM,CAAC,YAAY,CAAC;UACjCrc,OAAO,EAAE,mCAAmC;UAC5CuX,QAAQ,EAAE;QACd,CAAC;MAET,CAAC,EACD;QACIJ,OAAO,EAAE,CAAC;QACVwE,MAAM,EAAEA,CAAA,KAAM;UACV,oBACIpc,KAAA,CAAAsb,aAAA,CAAAtb,KAAA,CAAAqc,QAAA,QACK,IAAI,CAACrV,KAAK,CAACoQ,UAAU,IACtB,IAAI,CAACpQ,KAAK,CAACoQ,UAAU,GACjBC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,gBACtBxX,KAAA,CAAAsb,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAM,UACM,EAAC,GAAG,eACZ5b,KAAA,CAAAsb,aAAA,CAACvX,SAAS;YACN8X,SAAS,EAAC,mBAAmB;YAC7B/G,KAAK,EAAE,IAAK;YACZiI,MAAM,EAAC,IAAI;YACX9S,KAAK,EAAE,IAAI,CAACjD,KAAK,CAACoQ,UAAW;YAC7B4F,QAAQ,EAAEA,CAAA,KAAM;cACZ,IAAI,CAACvF,oBAAoB,CAAC,CAAC;YAC/B,CAAE;YAAA8D,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CACL,CACC,CAAC,gBAEP5b,KAAA,CAAAsb,aAAA,CAAChb,MAAM;YACH2c,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACjG,aAAa,CAAC,CAAE;YACpCxO,IAAI,EAAC,SAAS;YACdqT,SAAS,EAAC,yBAAyB;YAAAN,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,gBAEnC5b,KAAA,CAAAsb,aAAA,CAACxZ,YAAY;YAAAyZ,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,CAAE,CAAC,EAAC,GAAG,EACnB,UAAUZ,eAAe,EACtB,CACX,EACA,IAAI,CAAChU,KAAK,CAACT,aAAa,iBACrBvG,KAAA,CAAAsb,aAAA;YAAAC,MAAA;YAAAC,QAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAA,GAAG,yCAEA,CAET,CAAC;QAEX;MACJ,CACJ,CAAC;IACL;IACA,IAAIa,mBAAmB,EAAE;MACrB7S,IAAI,CAAC8C,MAAM,CAACmI,IAAI,CAAC,IAAI,CAAC6C,oBAAoB,CAACC,iBAAiB,CAAC,CAAC;IAClE;IAEA,OAAO/N,IAAI;EACf;EAEAgT,YAAYA,CAAA,EAAG;IACX,OAAO;MACHrU,GAAG,EAAE,yBAAyB;MAC9BqP,OAAO,EAAE,CAAC;MACVwE,MAAM,EAAEA,CAAA,KAAM;QAAA,IAAAc,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACV,oBACIxd,KAAA,CAAAsb,aAAA,CAAAtb,KAAA,CAAAqc,QAAA,qBACIrc,KAAA,CAAAsb,aAAA,CAAClX,WAAW;UACRqZ,cAAc,GAAAP,sBAAA,GACV,IAAI,CAAClW,KAAK,CAAC1B,QAAQ,cAAA4X,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBjV,SAAS,cAAAkV,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCO,UAAU,cAAAN,sBAAA,wBAAAC,sBAAA,GAA1CD,sBAAA,CAA4CzS,IAAI,cAAA0S,sBAAA,uBAAhDA,sBAAA,CACM1S,IACT;UACDgT,aAAa,EAAE,IAAI,CAAChP,iBAAkB;UACtCiP,SAAS,GAAAN,sBAAA,GACL,IAAI,CAACtW,KAAK,CAAC1B,QAAQ,cAAAgY,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBxP,iBAAiB,CAAC,CAAC,CAAC,cAAAyP,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CACM5M,WAAW,cAAA6M,sBAAA,uBADjBA,sBAAA,CACmBK,2BACtB;UACD9S,OAAO,EAAE,IAAI,CAACA,OAAQ;UAAAwQ,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CACzB,CACH,CAAC;MAEX;IACJ,CAAC;EACL;EAEAlQ,aAAaA,CAAA,EAAG;IAAA,IAAAoS,sBAAA,EAAAC,uBAAA;IACZ,QAAAD,sBAAA,GAAO,IAAI,CAAC7Y,KAAK,CAACyL,iBAAiB,cAAAoN,sBAAA,wBAAAC,uBAAA,GAA5BD,sBAAA,CAA8BnN,WAAW,cAAAoN,uBAAA,uBAAzCA,uBAAA,CACH,wCAAwC,IAAI,CAAC9Y,KAAK,CAACqF,YAAY,SAAS,CAC3E;EACL;EAEAqJ,4BAA4BA,CAAA,EAAG;IAAA,IAAAqK,uBAAA;IAC3B,IAAI,IAAI,CAAChX,KAAK,CAACR,sBAAsB,EAAE;MACnC,OAAOiJ,IAAI,CAACE,SAAS,CAAC;QAClBwF,gBAAgB,EAAE,IAAI,CAACnO,KAAK,CAACR,sBAAsB,CAACqJ,MAAM,CACrDoO,eAAe,IAAKA,eAAe,CAACC,IAAI,IAAI,IACjD;MACJ,CAAC,CAAC;IACN;IACA,OAAOza,2BAA2B,CAC9B,IAAI,CAACwB,KAAK,CAACqF,YAAY,GAAA0T,uBAAA,GACvB,IAAI,CAAC/Y,KAAK,CAACyL,iBAAiB,cAAAsN,uBAAA,uBAA5BA,uBAAA,CAA8BrN,WAClC,CAAC;EACL;EAEAlE,yBAAyBA,CAAA,EAAG;IACxB,IAAI0R,YAAY,GAAG/a,uBAAuB,CACtC,IAAI,CAACuQ,4BAA4B,CAAC,CAAC,EACnCpO,SAAS,EACT,KAAK,EACL,IAAI,EACJ,IAAI,CAACwF,OAAO,EACZ,MAAM;MACF,IAAI,IAAI,CAACW,aAAa,CAAC,CAAC,EAAE;QACtB,IAAI,CAACL,kBAAkB,CACnB,CAAC,CAAC,EACF,IAAI,CAACN,OAAO,CAACnG,OAAO,CAACiH,cAAc,CAAC,CACxC,CAAC;MACL,CAAC,MAAM;QACHxE,OAAO,CAAC8D,GAAG,CAAC,oBAAoB,CAAC;MACrC;IACJ,CAAC,EACD,KAAK,EACL,IAAI,CAAC0L,iBAAiB,CAAC,CAAC,EACvBpL,OAAO,IAAK,IAAI,CAACD,mBAAmB,CAACC,OAAO,CACjD,CAAC;IACD;IACA,MAAM7B,IAAI,GAAG;MACT8S,OAAO,EAAE,CAAC;MACVC,cAAc,EAAE,IAAI;MACpBjQ,MAAM,EACFyR,YAAY,CAACzU,MAAM,GAAG,CAAC,GACjB;MACI;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAGyU,YAAY,CAACC,GAAG,CAAElW,SAAS,IAAK;QAC/BA,SAAS,CAAC0P,OAAO,GAAG1P,SAAS,CAAC0P,OAAO,IAAI,CAAC;QAC1C,OAAO1P,SAAS;MACpB,CAAC,CAAC,CACL,GACD;IACd,CAAC;IACD,OAAO0B,IAAI;EACf;EAEAqR,oBAAoBA,CAAA,EAAG;IAAA,IAAAoD,uBAAA;IACnB,IAAI/T,YAAY,GAAG,IAAI,CAACrF,KAAK,CAACqF,YAAY;IAC1C,IAAIqK,QAAQ,IAAA0J,uBAAA,GAAG,IAAI,CAACpZ,KAAK,CAACyL,iBAAiB,cAAA2N,uBAAA,uBAA5BA,uBAAA,CAA8B1J,QAAQ;IACrD,IAAI2J,uBAAuB;IAC3B,IAAI3J,QAAQ,EACRA,QAAQ,CAACyJ,GAAG,CAAExJ,YAAY,IAAK;MAC3B,IAAIA,YAAY,CAAC3K,KAAK,IAAIK,YAAY,EAAE;QACpCgU,uBAAuB,GAAG1J,YAAY;MAC1C;IACJ,CAAC,CAAC;IACN,OAAO0J,uBAAuB;EAClC;EAEAC,cAAcA,CAACC,OAAO,EAAEC,KAAK,EAAElP,aAAa,EAAE;IAC1C,IAAImP,iBAAiB,GAAG,IAAI,CAAC1X,KAAK,CAACnB,cAAc;IACjD6Y,iBAAiB,CAACF,OAAO,CAAC,GAAGC,KAAK;IAClC;IACA;IACA;IACA;IACA,IAAI5X,sBAAsB,GAAG,IAAI,CAACG,KAAK,CAACH,sBAAsB;IAC9DA,sBAAsB,CAAC2X,OAAO,CAAC,GAAGjP,aAAa;IAE/C,IAAI,CAACpI,QAAQ,CAAC;MACVN;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACG,KAAK,CAACO,eAAe,EAAE;MAC5B,IAAI8F,WAAW,GAAGqR,iBAAiB;MACnCxa,YAAY,CAACgH,WAAW,CACpB,IAAI,CAAC3B,WAAW,CAAC,CAAC,EAClB8D,WAAW,EACX,aACJ,CAAC;IACL;EACJ;EAEAsR,iBAAiBA,CAACH,OAAO,EAAEC,KAAK,EAAE;IAC9B,IAAIC,iBAAiB,GAAG,IAAI,CAAC1X,KAAK,CAAChB,sBAAsB;IACzD0Y,iBAAiB,CAACF,OAAO,CAAC,GAAGC,KAAK;IAClC;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACzX,KAAK,CAACO,eAAe,EAAE;MAC5B,IAAIqX,QAAQ,GAAGF,iBAAiB;MAChCxa,YAAY,CAACgH,WAAW,CAAC,IAAI,CAAC3B,WAAW,CAAC,CAAC,EAAEqV,QAAQ,EAAE,WAAW,CAAC;IACvE;EACJ;EAEAC,oBAAoBA,CAACL,OAAO,EAAEC,KAAK,EAAE;IACjC,IAAIC,iBAAiB,GAAG,IAAI,CAAC1X,KAAK,CAACb,yBAAyB;IAC5DuY,iBAAiB,CAACF,OAAO,CAAC,GAAGC,KAAK;IAClC;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACzX,KAAK,CAACO,eAAe,EAAE;MAC5B,IAAIuX,WAAW,GAAGJ,iBAAiB;MACnCxa,YAAY,CAACgH,WAAW,CACpB,IAAI,CAAC3B,WAAW,CAAC,CAAC,EAClBuV,WAAW,EACX,cACJ,CAAC;IACL;EACJ;EAEAC,yBAAyBA,CAACP,OAAO,EAAEQ,OAAO,EAAE;IACxC,IAAIC,mBAAmB,GAAG,IAAI,CAACjY,KAAK,CAAClB,wBAAwB;IAC7DmZ,mBAAmB,CAACT,OAAO,CAAC,GAAGQ,OAAO;IACtC,IAAI,CAAC7X,QAAQ,CAAC;MACVrB,wBAAwB,EAAEmZ;IAC9B,CAAC,CAAC;EACN;EAEAC,4BAA4BA,CAACV,OAAO,EAAEQ,OAAO,EAAE;IAC3C,IAAIC,mBAAmB,GAAG,IAAI,CAACjY,KAAK,CAACf,2BAA2B;IAChEgZ,mBAAmB,CAACT,OAAO,CAAC,GAAGQ,OAAO;IACtC,IAAI,CAAC7X,QAAQ,CAAC;MACVlB,2BAA2B,EAAEgZ;IACjC,CAAC,CAAC;EACN;EAEAE,+BAA+BA,CAACX,OAAO,EAAEQ,OAAO,EAAE;IAC9C,IAAIC,mBAAmB,GAAG,IAAI,CAACjY,KAAK,CAACZ,8BAA8B;IACnE6Y,mBAAmB,CAACT,OAAO,CAAC,GAAGQ,OAAO;IACtC,IAAI,CAAC7X,QAAQ,CAAC;MACVf,8BAA8B,EAAE6Y;IACpC,CAAC,CAAC;EACN;EAEAG,wBAAwBA,CAAA,EAAG;IACvB,IAAI;MACAtZ,wBAAwB;MACxBG,2BAA2B;MAC3BG;IACJ,CAAC,GAAG,IAAI,CAACY,KAAK;IACd,IAAIqY,QAAQ,GAAG,KAAK;IACpB7V,MAAM,CAACC,IAAI,CAAC3D,wBAAwB,CAAC,CAACsY,GAAG,CAAEI,OAAO,IAAK;MACnD,IAAI,CAAC1Y,wBAAwB,CAAC0Y,OAAO,CAAC,EAAE;QACpCa,QAAQ,GAAG,IAAI;MACnB;IACJ,CAAC,CAAC;IACF7V,MAAM,CAACC,IAAI,CAACxD,2BAA2B,CAAC,CAACmY,GAAG,CAAEI,OAAO,IAAK;MACtD,IAAI,CAACvY,2BAA2B,CAACuY,OAAO,CAAC,EAAE;QACvCa,QAAQ,GAAG,IAAI;MACnB;IACJ,CAAC,CAAC;IACF7V,MAAM,CAACC,IAAI,CAACrD,8BAA8B,CAAC,CAACgY,GAAG,CAAEI,OAAO,IAAK;MACzD,IAAI,CAACpY,8BAA8B,CAACoY,OAAO,CAAC,EAAE;QAC1Ca,QAAQ,GAAG,IAAI;MACnB;IACJ,CAAC,CAAC;IACF,OAAO,CAACA,QAAQ;EACpB;EA+BAjD,MAAMA,CAAA,EAAG;IAAA,IAAAkD,mBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACL,MAAM;MAAE5V;IAAW,CAAC,GAAG,IAAI,CAAC9E,KAAK;IACjC,MAAM;MACFI,gBAAgB;MAChBD,OAAO;MACPI,iBAAiB;MACjBE,KAAK;MACLJ,QAAQ;MACRK,WAAW;MACXia,WAAW;MACXha,YAAY;MACZG,WAAW;MACXG,cAAc;MACd2Z,WAAW;MACXxZ,iBAAiB;MACjBC,YAAY;MACZG,2BAA2B;MAC3BC;IACJ,CAAC,GAAG,IAAI,CAACM,KAAK;IACd;IACA,MAAM0J,iBAAiB,GAAG,IAAI,CAACuK,oBAAoB,CAAC,CAAC;IAErD,MAAM6E,kBAAkB,GACpB,CAAAxa,QAAQ,aAARA,QAAQ,wBAAAga,mBAAA,GAARha,QAAQ,CAAE2C,SAAS,cAAAqX,mBAAA,uBAAnBA,mBAAA,CAAqBS,gBAAgB,KAAI,IAAI,GACvC,EAAE,GACFza,QAAQ,aAARA,QAAQ,wBAAAia,oBAAA,GAARja,QAAQ,CAAE2C,SAAS,cAAAsX,oBAAA,uBAAnBA,oBAAA,CAAqBQ,gBAAgB;IAE/C,IAAIC,WAAW,GAAG,aAAa;IAC/B,IAAIC,WAAW,GAAG,SAAS;IAC3B,IAAIC,mBAAmB,GAAG,KAAK;IAC/B,IAAI7W,SAAS,GAAG,IAAI;IACpB;IACA,IAAI,IAAI,CAACrC,KAAK,CAACO,eAAe,EAAE;MAC5B8B,SAAS,GAAG,IAAI,CAACqC,aAAa,CAAC,CAAC,GAC1BxH,YAAY,CAACoF,UAAU,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,GACjDrF,YAAY,CAACoF,UAAU,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;MACjD,IAAIF,SAAS,IAAIG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;QAChDwW,mBAAmB,GAAG,IAAI;MAC9B;IACJ;IACA,IAAIC,gBAAgB,GAAG;MACnB,KAAAX,sBAAA,GAAG,IAAI,CAACxY,KAAK,CAAC1B,QAAQ,cAAAka,sBAAA,wBAAAC,sBAAA,GAAnBD,sBAAA,CAAqBvX,SAAS,cAAAwX,sBAAA,uBAA9BA,sBAAA,CAAgCxX,SAAS;MAC5C,GAAGoB;IACP,CAAC;IACD8W,gBAAgB,GAAG,IAAI,CAAC9Q,4BAA4B,CAChD8Q,gBAAgB,EAChB,IAAI,CAACnZ,KAAK,CAACH,sBACf,CAAC;IACD,IAAIuZ,eAAe,GAAGje,0BAA0B,CAC5Cge,gBAAgB,EAChB,IAAI,CAAClI,kBAAkB,CAAC,CAAC,CAACvL,MAC9B,CAAC;IACD,MAAM2T,qBAAqB,GAAG,IAAI,CAACjB,wBAAwB,CAAC,CAAC;IAE7D,IAAI1O,iBAAiB,EAAE;MACnBsP,WAAW,GAAG,iBAAiB,GAAGtP,iBAAiB,CAACoE,KAAK;MACzDmL,WAAW,GAAGvP,iBAAiB,CAAC4P,KAAK;IACzC,CAAC,MAAM,IACH,IAAI,CAACrb,KAAK,CAACqF,YAAY,IAAI,oBAAoB,IAC/C,IAAI,CAACrF,KAAK,CAACqF,YAAY,IAAI,kBAAkB,EAC/C;MAAA,IAAAiW,gBAAA;MACEP,WAAW,GACP,IAAI,CAAC/a,KAAK,CAACqF,YAAY,IAAI,oBAAoB,GACzC,eAAe,GACf,aAAa;MACvB2V,WAAW,GACP,IAAI,CAAChb,KAAK,CAACqF,YAAY,IAAI,oBAAoB,GACzC,SAAS,GACT,SAAS;MACnB,IAAI+C,WAAW,IAAAkT,gBAAA,GAAGH,eAAe,cAAAG,gBAAA,uBAAfA,gBAAA,CAAiBlT,WAAW;MAC9C+S,eAAe,GAAG,CAAC,CAAC;MACpBA,eAAe,CAAC,aAAa,CAAC,GAAG/S,WAAW,GAAGA,WAAW,GAAG,CAAC,CAAC;IACnE;IACA,IAAI,CAAC,IAAI,CAACrG,KAAK,CAACI,eAAe,EAAE;MAC7BvE,+BAA+B,CAACud,eAAe,EAAE,IAAI,CAACrV,OAAO,CAAC;IAClE;IAEA,oBACI/K,KAAA,CAAAsb,aAAA,CAACpb,KAAK;MACF4U,KAAK,eACD9U,KAAA,CAAAsb,aAAA;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,gBACI5b,KAAA,CAAAsb,aAAA;QACIjH,KAAK,EAAE;UAAEiM,KAAK,EAAEL;QAAY,CAAE;QAC9BpE,SAAS,EAAE,2DAA4D;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAC1E,CAAC,EACDoE,WACC,CACT;MACD5a,OAAO,EAAEA,OAAQ;MACjBob,IAAI,EAAE,IAAI,CAAChZ,QAAS;MACpBiZ,cAAc,EAAEpb,gBAAiB;MACjC0W,KAAK,EAAE,IAAK;MACZ1H,KAAK,EAAE;QACHC,SAAS,EAAE;MACf,CAAE;MACFoM,SAAS,EAAE;QACPC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE;MAChB,CAAE;MACFC,MAAM,EAAE,IAAK;MACbC,QAAQ,EAAE,IAAI,CAACrZ,YAAa;MAC5BsZ,UAAU,EACN,IAAI,CAACha,KAAK,CAACO,eAAe,IAC1BrD,YAAY,CAACoF,UAAU,CACnB,IAAI,CAACC,WAAW,CAAC,CAAC,EAClB,IAAI,CAACmC,aAAa,CAAC,CACvB,CAAC,GACK,IAAI,CAAC0I,mBAAmB,GACxB,IACT;MAAAmH,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEApW,iBAAiB,gBACdxF,KAAA,CAAAsb,aAAA;MAAKO,SAAS,EAAC,mCAAmC;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9C5b,KAAA,CAAAsb,aAAA,CAAC9Z,gBAAgB;MAAA+Z,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAClB,CAAC,GACNtW,QAAQ,IAAIC,SAAS,gBACrBvF,KAAA,CAAAsb,aAAA;MAAGO,SAAS,EAAC,aAAa;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAElW,KAAS,CAAC,gBAEtC1F,KAAA,CAAAsb,aAAA,CAAAtb,KAAA,CAAAqc,QAAA,qBACIrc,KAAA,CAAAsb,aAAA,CAAC5a,GAAG;MAACmb,SAAS,EAAC,SAAS;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACpB5b,KAAA,CAAAsb,aAAA,CAAC3a,GAAG;MACAsgB,EAAE,EAAE,EAAG;MACPC,EAAE,EAAE,EAAG;MACPrF,SAAS,EAAC,yBAAyB;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAElCkE,kBAAkB,CAACpW,MAAM,GAAG,CAAC,gBAC1B1J,KAAA,CAAAsb,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACI5b,KAAA,CAAAsb,aAAA;MAAIO,SAAS,EAAC,gBAAgB;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,0CAEpB,EAAC,GAAG,EACV,IAAI,CAACpH,0BAA0B,CAC5BsL,kBACJ,CACA,CACH,CAAC,gBAEN9f,KAAA,CAAAsb,aAAA,CAAAtb,KAAA,CAAAqc,QAAA,MAAI,CACP,eAEDrc,KAAA,CAAAsb,aAAA;MAAKO,SAAS,EAAC,wDAAwD;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACnE5b,KAAA,CAAAsb,aAAA;MAAKO,SAAS,EAAC,eAAe;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1B5b,KAAA,CAAAsb,aAAA;MAAKO,SAAS,EAAC,SAAS;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,gBACP,EAAC7R,UAAU,CAACoX,IAAI,EAAC,IAAE,EAAC,GAAG,eACrCnhB,KAAA,CAAAsb,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACI5b,KAAA,CAAAsb,aAAA,CAACtY,QAAQ;MAACoe,IAAI,EAAErX,UAAW;MAAAwR,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAC3B,CACL,CAAC,EACL7R,UAAU,CAAC+K,KACX,CAAC,eACN9U,KAAA,CAAAsb,aAAA;MAAKO,SAAS,EAAC,wCAAwC;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACnD5b,KAAA,CAAAsb,aAAA,CAACna,QAAQ;MACLkgB,KAAK;MACL;MAAA;MAAA9F,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAEA5b,KAAA,CAAAsb,aAAA,CAACna,QAAQ,CAACmgB,KAAK;MACXC,MAAM,EACFxX,UAAU,CAAC8H,gBAAgB,CACtB2P,WACR;MAAAjG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAED5b,KAAA,CAAAsb,aAAA;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACI5b,KAAA,CAAAsb,aAAA,CAACvY,eAAe;MACZqe,IAAI,EAAErX,UAAW;MACjB+D,iBAAiB,EACb,EAAA4R,sBAAA,OAAI,CAAC1Y,KAAK,CAAC1B,QAAQ,cAAAoa,sBAAA,uBAAnBA,sBAAA,CACM5R,iBAAiB,CAClBpE,MAAM,IAAG,CAAC,IAAAiW,sBAAA,GACT,IAAI,CAAC3Y,KAAK,CACL1B,QAAQ,cAAAqa,sBAAA,uBADbA,sBAAA,CAEM7R,iBAAiB,CAAC,CAAC,CAAC,GAC1B,CAAC,CACV;MAAAyN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACJ,CACA,CACO,CACV,CACT,CACJ,CAAC,EACLsE,mBAAmB,iBAChBlgB,KAAA,CAAAsb,aAAA,CAACja,KAAK;MACFwa,SAAS,EAAC,SAAS;MACnBpb,OAAO,EAAC,+CAA+C;MACvD+H,IAAI,EAAC,OAAO;MACZiZ,QAAQ;MAAAlG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACX,CACJ,eACD5b,KAAA,CAAAsb,aAAA,CAACnb;IACG;IAAA;MACA0b,SAAS,EAAC,kBAAkB;MAC5B6F,MAAM,EAAC,UAAU;MACjBC,aAAa,EAAEvB,eAAgB;MAC/BwB,GAAG,EAAE,IAAI,CAAC7W,OAAQ;MAClBiS,QAAQ,EAAGrS,IAAI,IAAK;QAChB,IAAI,IAAI,CAAC3D,KAAK,CAACO,eAAe,EAAE;UAC5BrD,YAAY,CAACgH,WAAW,CACpB,IAAI,CAAC3B,WAAW,CAAC,CACrB,CAAC;QACL;QACA,IAAI,CAACuC,UAAU,CAACnB,IAAI,CAAC;MACzB,CAAE;MACFkX,QAAQ,EAAEpb,2BAA4B;MACtCqb,cAAc,EAAEA,CACZpZ,aAAa,EACbC,SAAS,KACR;QACD,IAAI,IAAI,CAAC+C,aAAa,CAAC,CAAC,EAAE;UACtB,IAAI,CAACL,kBAAkB,CACnB3C,aAAa,EACbC,SACJ,CAAC;QACL,CAAC,MAAM;UACH,IAAI,IAAI,CAAC3B,KAAK,CAACO,eAAe,EAAE;YAC5BrD,YAAY,CAACgH,WAAW,CACpB,IAAI,CAAC3B,WAAW,CAAC,CACrB,CAAC;UACL;UACAlC,OAAO,CAAC8D,GAAG,CAAC,oBAAoB,CAAC;QACrC;MACJ,CAAE;MAAAoQ,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEDnV,2BAA2B,iBACxBzG,KAAA,CAAAsb,aAAA,CAAAtb,KAAA,CAAAqc,QAAA,qBACIrc,KAAA,CAAAsb,aAAA;MAAKO,SAAS,EAAC,eAAe;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC1B5b,KAAA,CAAAsb,aAAA,CAACla,IAAI;MAAAma,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CACN,CACP,CACL,eACD5b,KAAA,CAAAsb,aAAA,CAAC5a,GAAG;MACA2T,KAAK,EAAE;QACH0N,aAAa,EAAE;MACnB,CAAE;MAAAxG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAEF5b,KAAA,CAAAsb,aAAA,CAAC3a,GAAG;MAACsgB,EAAE,EAAE,EAAG;MAACpF,SAAS,EAAC,SAAS;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC5B5b,KAAA,CAAAsb,aAAA,CAACha,WAAW;MACRiH,GAAG,EAAC,MAAM;MACVqB,IAAI,EAAE,IAAI,CAACqO,kBAAkB,CAAC,CAAE;MAChC+J,IAAI,EAAE,IAAI,CAACjX,OAAQ;MAAAwQ,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACtB,CACA,CAAC,eACN5b,KAAA,CAAAsb,aAAA,CAAC3a,GAAG;MACAsgB,EAAE,EAAE,EAAG;MACPC,EAAE,EAAE,EAAG;MACPrF,SAAS,EAAC,SAAS;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAElB7V,WAAW,CAACqY,GAAG,CACZ,CAAC6D,gBAAgB,EAAEC,KAAK;MAAA,IAAAC,iBAAA,EAAAC,qBAAA;MAAA,oBACpBpiB,KAAA,CAAAsb,aAAA,CAAC3a,GAAG;QACAsgB,EAAE,EAAE,EAAG;QACPC,EAAE,EAAE,EAAG;QACPrF,SAAS,EAAC,SAAS;QACnBtT,GAAG,EACC0Z,gBAAgB,CAAC1Z,GACpB;QAAAgT,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAEAqG,gBAAgB,CAACnN,KAAK,IACnB,EAAE,iBACF9U,KAAA,CAAAsb,aAAA;QAAIO,SAAS,EAAC,SAAS;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAEfqG,gBAAgB,CAACnN,KAAK,eAE1B9U,KAAA,CAAAsb,aAAA;QAAIO,SAAS,EAAC,YAAY;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAK,CAC/B,CACP,eAQD5b,KAAA,CAAAsb,aAAA,CAACzX,UAAU;QACPwe,SAAS,EAAE9gB,UAAU,CAAC+gB,YAAY,CAAC,CAAE;QACrCC,YAAY,EAAEhhB,UAAU,CAACihB,YAAY,CAAC,CAAE;QACxCC,YAAY,EACR,IAAI,CAACzb,KAAK,CACLvB,QAAQ,IAAA0c,iBAAA,GACP/B,eAAe,cAAA+B,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CACM5U,SAAS,cAAA6U,qBAAA,uBADfA,qBAAA,CAEIH,gBAAgB,CACX1Z,GAAG,CACX,GACD,EACT;QACDgW,cAAc,EACVE,KAAK,IACJ;UACD,IAAI,CAACE,iBAAiB,CAClBsD,gBAAgB,CAAC1Z,GAAG,EACpBkW,KACJ,CAAC;QACL,CAAE;QACFiE,mBAAmB,EACf1D,OAAO,IACN;UACD,IAAI,CAACE,4BAA4B,CAC7B+C,gBAAgB,CAAC1Z,GAAG,EACpByW,OACJ,CAAC;QACL,CAAE;QAAAzD,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CACL,CACA,CAAC;IAAA,CAEd,CACC,CAAC,eACN5b,KAAA,CAAAsb,aAAA,CAAC3a,GAAG;MACAsgB,EAAE,EAAE,EAAG;MACPC,EAAE,EAAE,EAAG;MACPrF,SAAS,EAAC,SAAS;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAElB1V,cAAc,CAACkY,GAAG,CACf,CACIuE,mBAAmB,EACnBT,KAAK;MAAA,IAAAU,iBAAA,EAAAC,qBAAA;MAAA,oBAEL7iB,KAAA,CAAAsb,aAAA,CAAC3a,GAAG;QACAsgB,EAAE,EAAE,EAAG;QACPC,EAAE,EAAE,EAAG;QACPrF,SAAS,EAAC,SAAS;QACnBtT,GAAG,EACCoa,mBAAmB,CAACpa,GACvB;QAAAgT,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAEA+G,mBAAmB,CAAC7N,KAAK,IACtB,EAAE,iBACF9U,KAAA,CAAAsb,aAAA,CAAAtb,KAAA,CAAAqc,QAAA,qBACIrc,KAAA,CAAAsb,aAAA;QAAIO,SAAS,EAAC,SAAS;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAClB+G,mBAAmB,CAAC3K,QAAQ,iBACzBhY,KAAA,CAAAsb,aAAA;QACIjH,KAAK,EAAE;UACHiM,KAAK,EAAE;QACX,CAAE;QAAA/E,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAED,GAAG,EAAC,GACJ,EAAC,GACA,CACT,EAEG+G,mBAAmB,CAAC7N,KAAK,eAE7B9U,KAAA,CAAAsb,aAAA;QAAIO,SAAS,EAAC,YAAY;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAK,CAC/B,CACN,CACL,eAQD5b,KAAA,CAAAsb,aAAA,CAACxX,WAAW;QACR8d,GAAG,EAAE,IAAI,CAAC9Z,SAAU;QACpBua,SAAS,EAAE9gB,UAAU,CAAC+gB,YAAY,CAAC,CAAE;QACrCC,YAAY,EAAEhhB,UAAU,CAACihB,YAAY,CAAC,CAAE;QACxCxK,QAAQ,EACJ2K,mBAAmB,CAAC3K,QACvB;QACDyK,YAAY,EACR,IAAI,CAACzb,KAAK,CACLvB,QAAQ,IAAAmd,iBAAA,GACPxC,eAAe,cAAAwC,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CACMnV,YAAY,cAAAoV,qBAAA,uBADlBA,qBAAA,CAEIF,mBAAmB,CACdpa,GAAG,CACX,GACD,EACT;QACDgW,cAAc,EACVE,KAAK,IACJ;UACD,IAAI,CAACI,oBAAoB,CACrB8D,mBAAmB,CAACpa,GAAG,EACvBkW,KACJ,CAAC;QACL,CAAE;QACFiE,mBAAmB,EACf1D,OAAO,IACN;UACD,IAAI,CAACG,+BAA+B,CAChCwD,mBAAmB,CAACpa,GAAG,EACvByW,OACJ,CAAC;QACL,CAAE;QAAAzD,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CACL,CACA,CAAC;IAAA,CAEd,CACC,CAAC,eACN5b,KAAA,CAAAsb,aAAA,CAAC3a,GAAG;MACAsgB,EAAE,EAAE,EAAG;MACPC,EAAE,EAAE,EAAG;MACPrF,SAAS,EAAC,SAAS;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAElBhW,YAAY,CAACwY,GAAG,CACb,CAAC0E,iBAAiB,EAAEZ,KAAK;MAAA,IAAAa,iBAAA,EAAAC,qBAAA;MAAA,oBACrBhjB,KAAA,CAAAsb,aAAA,CAAC3a,GAAG;QACAsgB,EAAE,EAAE,EAAG;QACPC,EAAE,EAAE,EAAG;QACPrF,SAAS,EAAC,SAAS;QACnBtT,GAAG,EACCua,iBAAiB,CAACva,GACrB;QAAAgT,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAGAkH,iBAAiB,CAAChO,KAAK,IACpB,EAAE,iBACF9U,KAAA,CAAAsb,aAAA;QAAIO,SAAS,EAAC,SAAS;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAClBkH,iBAAiB,CAAC9K,QAAQ,iBACvBhY,KAAA,CAAAsb,aAAA;QACIjH,KAAK,EAAE;UACHiM,KAAK,EAAE;QACX,CAAE;QAAA/E,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAED,GAAG,EAAC,GACJ,EAAC,GACA,CACT,EAEGkH,iBAAiB,CAAChO,KAAK,eAE3B9U,KAAA,CAAAsb,aAAA;QAAIO,SAAS,EAAC,YAAY;QAAAN,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CAAK,CAC/B,CACP,eAQD5b,KAAA,CAAAsb,aAAA,CAACnY;MACG;MACA;MAAA;QACA6U,QAAQ,EACJ8K,iBAAiB,CAAC9K,QACrB;QACDiL,UAAU,EAAE,CAAE;QACdZ,SAAS,EAAE9gB,UAAU,CAAC+gB,YAAY,CAAC,CAAE;QACrCC,YAAY,EAAEhhB,UAAU,CAACihB,YAAY,CAAC,CAAE;QACxCjE,cAAc,EAAEA,CACZE,KAAK,EACLyE,cAAc,EACd3T,aAAa,KACZ;UACD,IAAI,CAACgP,cAAc,CACfuE,iBAAiB,CAACva,GAAG,EACrBkW,KAAK,EACLlP,aACJ,CAAC;QACL,CAAE;QACF4T,oBAAoB,EAChBnE,OAAO,IACN;UACD,IAAI,CAACD,yBAAyB,CAC1B+D,iBAAiB,CAACva,GAAG,EACrByW,OACJ,CAAC;QACL,CAAE;QACFyD,YAAY,EACR,IAAI,CAACzb,KAAK,CACLvB,QAAQ,IAAAsd,iBAAA,GACP3C,eAAe,cAAA2C,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CACM1V,WAAW,cAAA2V,qBAAA,uBADjBA,qBAAA,CAEIF,iBAAiB,CACZva,GAAG,CACX,GACD,EACT;QACD6a,cAAc;QACdC,mBAAmB,EAAC,MAAM;QAC1BC,sBAAsB,EAAC,MAAM;QAC7BC,UAAU,EAAE;UACRC,IAAI,EAAE;QACV,CAAE;QAAAjI,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,CACL,CACA,CAAC;IAAA,CAEd,CACC,CACJ,CAAC,eACN5b,KAAA,CAAAsb,aAAA;MAAKO,SAAS,EAAC,SAAS;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACnB,IAAI,CAAC5N,qBAAqB,CAAC,CAAC,iBACzBhO,KAAA,CAAAsb,aAAA,CAAC3X,eAAe;MACZ0C,iBAAiB,EACbA,iBACH;MACDC,YAAY,EAAEA,YAAa;MAC3Bmd,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAACrT,iBAAiB,CAAC,CAAC;MAC5B,CAAE;MAAAmL,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACL,CAEJ,CAAC,eACN5b,KAAA,CAAAsb,aAAA;MAAKO,SAAS,EAAC,SAAS;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACnB,CAACyE,qBAAqB,iBACnBrgB,KAAA,CAAAsb,aAAA,CAACla,IAAI;MAAAma,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAO,CACf,eACD5b,KAAA,CAAAsb,aAAA,CAAChb,MAAM;MACHkI,IAAI,EAAC,SAAS;MACdkb,QAAQ,EAAC,QAAQ;MACjB,eAAY,eAAe;MAC3B7B,QAAQ,EACJxc,gBAAgB,IAChB,CAACgb,qBAAqB,IACtBP,kBAAkB,CAACpW,MAAM,GAAG,CAAC,IAC7BhD,uBAAuB,IACvBD,2BACH;MAAA8U,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEAoE,WACG,CAAC,EAER3a,gBAAgB,gBACbrF,KAAA,CAAAsb,aAAA;MAAKO,SAAS,EAAC,mCAAmC;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9C5b,KAAA,CAAAsb,aAAA,CAAC9Z,gBAAgB;MAAA+Z,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAClB,CAAC,GACN,IAAI,EACPlW,KAAK,gBACF1F,KAAA,CAAAsb,aAAA;MAAGO,SAAS,EAAC,aAAa;MAAAN,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACrBlW,KACF,CAAC,GACJ,IACH,CACH,CACL,CACJ,CACP,CAEH,CAAC;EAEhB;AACJ;AAEA,eAAeX,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}